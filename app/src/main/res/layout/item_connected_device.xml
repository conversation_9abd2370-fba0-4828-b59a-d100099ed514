<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:strokeColor="?attr/colorPrimary"
    app:strokeWidth="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivDeviceIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_watch"
            android:tint="?attr/colorPrimary"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDeviceName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                tools:text="Apple Watch Series 8" />

            <TextView
                android:id="@+id/tvDeviceAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="AA:BB:CC:DD:EE:FF" />

            <TextView
                android:id="@+id/tvDeviceInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="2dp"
                tools:text="Connected • Battery: 85%" />

            <TextView
                android:id="@+id/tvConnectionStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                android:textColor="?attr/colorPrimary"
                android:layout_marginTop="4dp"
                tools:text="Connected • Heart Rate: 72 BPM" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDisconnect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Disconnect"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_marginBottom="4dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSettings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Settings"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button.TextButton" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
