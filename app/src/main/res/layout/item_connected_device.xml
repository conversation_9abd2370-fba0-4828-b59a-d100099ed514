<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:gravity="center_vertical">

    <ImageView
        android:id="@+id/ivDeviceIcon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_heart_rate"
        app:tint="@color/device_icon_color" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvDeviceName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Apple Watch"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDeviceInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Connected • Battery: 85%"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp" />

    </LinearLayout>

    <TextView
        android:id="@+id/tvConnectionStatus"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/status_indicator_connected"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="Connected"
        android:textColor="@android:color/white"
        android:textSize="10sp" />

</LinearLayout>
