<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".ui.SettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Settings"
            app:navigationIcon="@drawable/ic_arrow_back" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Device Settings Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Device Settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManageDevices"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Manage Connected Devices"
                        android:drawableStart="@drawable/ic_bluetooth"
                        android:drawablePadding="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnScanDevices"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Scan for New Devices"
                        android:drawableStart="@drawable/ic_search"
                        android:drawablePadding="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Detection Settings Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Detection Settings"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Smoking Detection Sensitivity"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/sliderSmokingSensitivity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:valueFrom="0.1"
                        android:valueTo="1.0"
                        android:stepSize="0.1"
                        android:value="0.7"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Heart Rate Alert Thresholds"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="High Heart Rate Threshold (BPM)"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
                        android:layout_marginBottom="8dp">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etHighHeartRateThreshold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:text="100" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Low Heart Rate Threshold (BPM)"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etLowHeartRateThreshold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:text="60" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Notification Settings Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Notification Preferences"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchSmokingAlerts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Smoking Detection Alerts"
                        android:checked="true"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchHeartRateAlerts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Heart Rate Alerts"
                        android:checked="true"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchHealthRecommendations"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Health Recommendations"
                        android:checked="true"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchDailySummary"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Daily Summary"
                        android:checked="true"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switchMotivationalMessages"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Motivational Messages"
                        android:checked="true"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Daily Summary Time"
                        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnDailySummaryTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="8:00 PM"
                        android:drawableStart="@drawable/ic_schedule"
                        android:drawablePadding="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Data Management Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Data Management"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnExportData"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Export Data"
                        android:drawableStart="@drawable/ic_download"
                        android:drawablePadding="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_marginBottom="8dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnClearData"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Clear All Data"
                        android:drawableStart="@drawable/ic_delete"
                        android:drawablePadding="8dp"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        app:strokeColor="?attr/colorError"
                        android:textColor="?attr/colorError" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
