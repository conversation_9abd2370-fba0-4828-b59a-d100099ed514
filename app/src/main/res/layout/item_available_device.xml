<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageView
            android:id="@+id/ivDeviceIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_bluetooth"
            android:tint="?attr/colorOnSurfaceVariant"
            android:layout_marginEnd="16dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvDeviceName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                tools:text="Samsung Galaxy Watch 4" />

            <TextView
                android:id="@+id/tvDeviceAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                tools:text="11:22:33:44:55:66" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/tvSignalStrength"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:drawableStart="@drawable/ic_signal_strength"
                    android:drawablePadding="4dp"
                    android:gravity="center_vertical"
                    tools:text="Strong" />

                <TextView
                    android:id="@+id/tvDeviceType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.LabelSmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginStart="16dp"
                    tools:text="Wearable" />

            </LinearLayout>

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnConnect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Connect"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
