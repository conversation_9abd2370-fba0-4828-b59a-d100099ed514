<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Heart Rate Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_heart_rate"
                            app:tint="@color/heart_rate_color" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Heart Rate"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvHeartRateStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Disconnected"
                            android:textColor="@color/status_disconnected"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvCurrentHeartRate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="--"
                            android:textColor="@color/heart_rate_color"
                            android:textSize="36sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:text="BPM"
                            android:textColor="@color/heart_rate_color"
                            android:textSize="14sp" />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="1" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Baseline"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/tvBaselineHeartRate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="-- BPM"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvHeartRateZone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Heart Rate Zone: --"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Smoking Detection Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_smoking_detection" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Smoking Detection"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchSmokingDetection"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Today"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/tvTodaySmokingCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@color/smoking_count_color"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="This Week"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/tvWeekSmokingCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@color/smoking_count_color"
                                android:textSize="24sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Last Event"
                                android:textSize="12sp"
                                android:textColor="@android:color/darker_gray" />

                            <TextView
                                android:id="@+id/tvLastSmokingEvent"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="--"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnManualLog"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="Log Smoking Event"
                        app:icon="@drawable/ic_manual_log"
                        style="@style/Widget.Material3.Button.OutlinedButton" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Device Connection Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Connected Devices"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnScanDevices"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Scan"
                            style="@style/Widget.Material3.Button.TextButton" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvConnectedDevices"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="2"
                        tools:listitem="@layout/item_connected_device" />

                    <TextView
                        android:id="@+id/tvNoDevices"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:gravity="center"
                        android:text="No devices connected"
                        android:textColor="@android:color/darker_gray"
                        android:textSize="14sp"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Quick Actions -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnViewHistory"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="View History"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSettings"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Settings"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabEmergencyStop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_stop"
        app:backgroundTint="@color/emergency_color"
        app:tint="@android:color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
