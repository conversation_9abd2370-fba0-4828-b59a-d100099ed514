package com.thryve.exhale.notification

import android.content.Context
import android.content.SharedPreferences

/**
 * Manages notification preferences and settings
 */
class NotificationPreferences(context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "notification_preferences"
        
        // Preference keys
        private const val KEY_SMOKING_ALERTS_ENABLED = "smoking_alerts_enabled"
        private const val KEY_HEART_RATE_ALERTS_ENABLED = "heart_rate_alerts_enabled"
        private const val KEY_HEALTH_RECOMMENDATIONS_ENABLED = "health_recommendations_enabled"
        private const val KEY_DEVICE_STATUS_ENABLED = "device_status_enabled"
        private const val KEY_DAILY_SUMMARY_ENABLED = "daily_summary_enabled"
        private const val KEY_MOTIVATIONAL_MESSAGES_ENABLED = "motivational_messages_enabled"
        
        // Alert thresholds
        private const val KEY_HIGH_HEART_RATE_THRESHOLD = "high_heart_rate_threshold"
        private const val KEY_LOW_HEART_RATE_THRESHOLD = "low_heart_rate_threshold"
        private const val KEY_SMOKING_DETECTION_SENSITIVITY = "smoking_detection_sensitivity"
        
        // Timing preferences
        private const val KEY_DAILY_SUMMARY_TIME = "daily_summary_time"
        private const val KEY_HEALTH_REMINDER_FREQUENCY = "health_reminder_frequency"
        private const val KEY_MOTIVATIONAL_MESSAGE_FREQUENCY = "motivational_message_frequency"
        
        // Sound and vibration
        private const val KEY_NOTIFICATION_SOUND_ENABLED = "notification_sound_enabled"
        private const val KEY_NOTIFICATION_VIBRATION_ENABLED = "notification_vibration_enabled"
        
        // Default values
        private const val DEFAULT_HIGH_HEART_RATE_THRESHOLD = 100
        private const val DEFAULT_LOW_HEART_RATE_THRESHOLD = 60
        private const val DEFAULT_DAILY_SUMMARY_TIME = 20 // 8 PM
        private const val DEFAULT_HEALTH_REMINDER_FREQUENCY = 4 // hours
        private const val DEFAULT_MOTIVATIONAL_MESSAGE_FREQUENCY = 12 // hours
    }
    
    // Notification type enablement
    var smokingAlertsEnabled: Boolean
        get() = prefs.getBoolean(KEY_SMOKING_ALERTS_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_SMOKING_ALERTS_ENABLED, value).apply()
    
    var heartRateAlertsEnabled: Boolean
        get() = prefs.getBoolean(KEY_HEART_RATE_ALERTS_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_HEART_RATE_ALERTS_ENABLED, value).apply()
    
    var healthRecommendationsEnabled: Boolean
        get() = prefs.getBoolean(KEY_HEALTH_RECOMMENDATIONS_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_HEALTH_RECOMMENDATIONS_ENABLED, value).apply()
    
    var deviceStatusEnabled: Boolean
        get() = prefs.getBoolean(KEY_DEVICE_STATUS_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_DEVICE_STATUS_ENABLED, value).apply()
    
    var dailySummaryEnabled: Boolean
        get() = prefs.getBoolean(KEY_DAILY_SUMMARY_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_DAILY_SUMMARY_ENABLED, value).apply()
    
    var motivationalMessagesEnabled: Boolean
        get() = prefs.getBoolean(KEY_MOTIVATIONAL_MESSAGES_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_MOTIVATIONAL_MESSAGES_ENABLED, value).apply()
    
    // Alert thresholds
    var highHeartRateThreshold: Int
        get() = prefs.getInt(KEY_HIGH_HEART_RATE_THRESHOLD, DEFAULT_HIGH_HEART_RATE_THRESHOLD)
        set(value) = prefs.edit().putInt(KEY_HIGH_HEART_RATE_THRESHOLD, value).apply()
    
    var lowHeartRateThreshold: Int
        get() = prefs.getInt(KEY_LOW_HEART_RATE_THRESHOLD, DEFAULT_LOW_HEART_RATE_THRESHOLD)
        set(value) = prefs.edit().putInt(KEY_LOW_HEART_RATE_THRESHOLD, value).apply()
    
    var smokingDetectionSensitivity: Float
        get() = prefs.getFloat(KEY_SMOKING_DETECTION_SENSITIVITY, 0.7f)
        set(value) = prefs.edit().putFloat(KEY_SMOKING_DETECTION_SENSITIVITY, value).apply()
    
    // Timing preferences
    var dailySummaryTime: Int
        get() = prefs.getInt(KEY_DAILY_SUMMARY_TIME, DEFAULT_DAILY_SUMMARY_TIME)
        set(value) = prefs.edit().putInt(KEY_DAILY_SUMMARY_TIME, value).apply()
    
    var healthReminderFrequency: Int
        get() = prefs.getInt(KEY_HEALTH_REMINDER_FREQUENCY, DEFAULT_HEALTH_REMINDER_FREQUENCY)
        set(value) = prefs.edit().putInt(KEY_HEALTH_REMINDER_FREQUENCY, value).apply()
    
    var motivationalMessageFrequency: Int
        get() = prefs.getInt(KEY_MOTIVATIONAL_MESSAGE_FREQUENCY, DEFAULT_MOTIVATIONAL_MESSAGE_FREQUENCY)
        set(value) = prefs.edit().putInt(KEY_MOTIVATIONAL_MESSAGE_FREQUENCY, value).apply()
    
    // Sound and vibration
    var notificationSoundEnabled: Boolean
        get() = prefs.getBoolean(KEY_NOTIFICATION_SOUND_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_NOTIFICATION_SOUND_ENABLED, value).apply()
    
    var notificationVibrationEnabled: Boolean
        get() = prefs.getBoolean(KEY_NOTIFICATION_VIBRATION_ENABLED, true)
        set(value) = prefs.edit().putBoolean(KEY_NOTIFICATION_VIBRATION_ENABLED, value).apply()
    
    /**
     * Checks if a specific notification type should be shown
     */
    fun shouldShowNotification(type: NotificationType): Boolean {
        return when (type) {
            NotificationType.SMOKING_ALERT -> smokingAlertsEnabled
            NotificationType.HEART_RATE_ALERT -> heartRateAlertsEnabled
            NotificationType.HEALTH_RECOMMENDATION -> healthRecommendationsEnabled
            NotificationType.DEVICE_STATUS -> deviceStatusEnabled
            NotificationType.DAILY_SUMMARY -> dailySummaryEnabled
            NotificationType.MOTIVATIONAL_MESSAGE -> motivationalMessagesEnabled
        }
    }
    
    /**
     * Checks if heart rate value should trigger an alert
     */
    fun shouldTriggerHeartRateAlert(heartRate: Int): HeartRateAlertType? {
        if (!heartRateAlertsEnabled) return null
        
        return when {
            heartRate >= highHeartRateThreshold -> HeartRateAlertType.HIGH
            heartRate <= lowHeartRateThreshold -> HeartRateAlertType.LOW
            else -> null
        }
    }
    
    /**
     * Checks if smoking detection confidence meets threshold
     */
    fun shouldTriggerSmokingAlert(confidence: Float): Boolean {
        return smokingAlertsEnabled && confidence >= smokingDetectionSensitivity
    }
    
    /**
     * Gets formatted time string for daily summary
     */
    fun getDailySummaryTimeString(): String {
        val hour = dailySummaryTime
        val amPm = if (hour < 12) "AM" else "PM"
        val displayHour = if (hour == 0) 12 else if (hour > 12) hour - 12 else hour
        return "$displayHour:00 $amPm"
    }
    
    /**
     * Resets all preferences to default values
     */
    fun resetToDefaults() {
        prefs.edit().clear().apply()
    }
    
    /**
     * Exports preferences as a map for backup/restore
     */
    fun exportPreferences(): Map<String, Any> {
        return prefs.all.filterValues { it != null }.mapValues { it.value!! }
    }
    
    /**
     * Imports preferences from a map
     */
    fun importPreferences(preferences: Map<String, Any>) {
        val editor = prefs.edit()
        preferences.forEach { (key, value) ->
            when (value) {
                is Boolean -> editor.putBoolean(key, value)
                is Int -> editor.putInt(key, value)
                is Float -> editor.putFloat(key, value)
                is String -> editor.putString(key, value)
                is Long -> editor.putLong(key, value)
            }
        }
        editor.apply()
    }
}

/**
 * Types of notifications for preference management
 */
enum class NotificationType {
    SMOKING_ALERT,
    HEART_RATE_ALERT,
    HEALTH_RECOMMENDATION,
    DEVICE_STATUS,
    DAILY_SUMMARY,
    MOTIVATIONAL_MESSAGE
}
