package com.thryve.exhale.notification

import android.content.Context
import androidx.work.*
import com.thryve.exhale.data.repository.HeartRateRepository
import com.thryve.exhale.data.repository.SmokingEventRepository
import com.thryve.exhale.health.HealthRecommendationEngine
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Schedules periodic notifications and health reminders
 */
class NotificationScheduler(private val context: Context) {
    
    private val workManager = WorkManager.getInstance(context)
    
    companion object {
        const val DAILY_SUMMARY_WORK = "daily_summary_work"
        const val HEALTH_REMINDER_WORK = "health_reminder_work"
        const val MOTIVATION_WORK = "motivation_work"
        const val WEEKLY_REPORT_WORK = "weekly_report_work"
    }
    
    /**
     * Schedules all periodic notifications
     */
    fun scheduleAllNotifications() {
        scheduleDailySummary()
        scheduleHealthReminders()
        scheduleMotivationalMessages()
        scheduleWeeklyReport()
    }
    
    /**
     * Schedules daily summary notifications
     */
    private fun scheduleDailySummary() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val dailySummaryRequest = PeriodicWorkRequestBuilder<DailySummaryWorker>(1, TimeUnit.DAYS)
            .setConstraints(constraints)
            .setInitialDelay(calculateDelayUntilEvening(), TimeUnit.MILLISECONDS)
            .addTag(DAILY_SUMMARY_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            DAILY_SUMMARY_WORK,
            ExistingPeriodicWorkPolicy.REPLACE,
            dailySummaryRequest
        )
    }
    
    /**
     * Schedules health reminder notifications
     */
    private fun scheduleHealthReminders() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val healthReminderRequest = PeriodicWorkRequestBuilder<HealthReminderWorker>(4, TimeUnit.HOURS)
            .setConstraints(constraints)
            .addTag(HEALTH_REMINDER_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            HEALTH_REMINDER_WORK,
            ExistingPeriodicWorkPolicy.REPLACE,
            healthReminderRequest
        )
    }
    
    /**
     * Schedules motivational messages
     */
    private fun scheduleMotivationalMessages() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()
        
        val motivationRequest = PeriodicWorkRequestBuilder<MotivationWorker>(12, TimeUnit.HOURS)
            .setConstraints(constraints)
            .setInitialDelay(2, TimeUnit.HOURS) // Start after 2 hours
            .addTag(MOTIVATION_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            MOTIVATION_WORK,
            ExistingPeriodicWorkPolicy.REPLACE,
            motivationRequest
        )
    }
    
    /**
     * Schedules weekly report notifications
     */
    private fun scheduleWeeklyReport() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val weeklyReportRequest = PeriodicWorkRequestBuilder<WeeklyReportWorker>(7, TimeUnit.DAYS)
            .setConstraints(constraints)
            .setInitialDelay(calculateDelayUntilSunday(), TimeUnit.MILLISECONDS)
            .addTag(WEEKLY_REPORT_WORK)
            .build()
        
        workManager.enqueueUniquePeriodicWork(
            WEEKLY_REPORT_WORK,
            ExistingPeriodicWorkPolicy.REPLACE,
            weeklyReportRequest
        )
    }
    
    /**
     * Cancels all scheduled notifications
     */
    fun cancelAllNotifications() {
        workManager.cancelAllWorkByTag(DAILY_SUMMARY_WORK)
        workManager.cancelAllWorkByTag(HEALTH_REMINDER_WORK)
        workManager.cancelAllWorkByTag(MOTIVATION_WORK)
        workManager.cancelAllWorkByTag(WEEKLY_REPORT_WORK)
    }
    
    /**
     * Calculates delay until evening (8 PM)
     */
    private fun calculateDelayUntilEvening(): Long {
        val now = Calendar.getInstance()
        val evening = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 20) // 8 PM
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // If it's already past 8 PM today, schedule for tomorrow
            if (before(now)) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
        }
        
        return evening.timeInMillis - now.timeInMillis
    }
    
    /**
     * Calculates delay until Sunday evening
     */
    private fun calculateDelayUntilSunday(): Long {
        val now = Calendar.getInstance()
        val sunday = Calendar.getInstance().apply {
            set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
            set(Calendar.HOUR_OF_DAY, 19) // 7 PM
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
            
            // If it's already past this Sunday, schedule for next Sunday
            if (before(now)) {
                add(Calendar.WEEK_OF_YEAR, 1)
            }
        }
        
        return sunday.timeInMillis - now.timeInMillis
    }
}

/**
 * Worker for daily summary notifications
 */
class DailySummaryWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val notificationManager = ExhaleNotificationManager(applicationContext)
            val healthEngine = HealthRecommendationEngine()
            
            // TODO: Get actual data from repositories when DI is set up
            // For now, use placeholder data
            val smokingCount = 0 // smokingEventRepository.getTodaySmokingCount()
            val avgHeartRate = 0 // heartRateRepository.getTodayAverageHeartRate()
            
            val recommendations = listOf(
                "Stay hydrated throughout the day",
                "Take deep breaths when feeling stressed",
                "Consider a short walk to boost circulation"
            )
            
            notificationManager.showDailySummaryNotification(
                smokingCount = smokingCount,
                avgHeartRate = avgHeartRate,
                recommendations = recommendations
            )
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * Worker for health reminder notifications
 */
class HealthReminderWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val notificationManager = ExhaleNotificationManager(applicationContext)
            
            // Rotate through different health tips
            val healthTips = listOf(
                Pair("Stay Hydrated", "Drinking water helps reduce cravings and supports heart health."),
                Pair("Take Deep Breaths", "Practice breathing exercises to manage stress and reduce smoking triggers."),
                Pair("Move Your Body", "Even a 5-minute walk can boost your mood and reduce cravings."),
                Pair("Mindful Moments", "Take a moment to notice your surroundings and practice mindfulness."),
                Pair("Healthy Snacking", "Keep healthy snacks nearby to manage oral fixation habits.")
            )
            
            val randomTip = healthTips.random()
            notificationManager.showHealthRecommendation(
                title = randomTip.first,
                message = randomTip.second,
                recommendationType = HealthRecommendationType.GENERAL_HEALTH
            )
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * Worker for motivational messages
 */
class MotivationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val notificationManager = ExhaleNotificationManager(applicationContext)
            val healthEngine = HealthRecommendationEngine()
            
            // TODO: Calculate actual days since last smoke when data is available
            val daysSinceLastSmoke = 1
            val motivationalMessage = healthEngine.generateMotivationalMessage(daysSinceLastSmoke)
            
            notificationManager.showHealthRecommendation(
                title = "You're Doing Great!",
                message = motivationalMessage,
                recommendationType = HealthRecommendationType.SMOKING_CESSATION
            )
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}

/**
 * Worker for weekly report notifications
 */
class WeeklyReportWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val notificationManager = ExhaleNotificationManager(applicationContext)
            
            // TODO: Generate actual weekly report when data is available
            notificationManager.showHealthRecommendation(
                title = "Weekly Health Report",
                message = "Your weekly health summary is ready. Check your progress and see personalized recommendations.",
                recommendationType = HealthRecommendationType.GENERAL_HEALTH
            )
            
            Result.success()
        } catch (e: Exception) {
            Result.retry()
        }
    }
}
