package com.thryve.exhale.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.thryve.exhale.R
import com.thryve.exhale.data.model.HeartRateData
import com.thryve.exhale.data.model.SmokingEvent
import com.thryve.exhale.ui.MainActivity
import java.text.SimpleDateFormat
import java.util.*

/**
 * Centralized notification manager for all app notifications
 */
class ExhaleNotificationManager(private val context: Context) {
    
    private val notificationManager = NotificationManagerCompat.from(context)
    
    companion object {
        // Notification Channels
        const val CHANNEL_SMOKING_ALERTS = "smoking_alerts"
        const val CHANNEL_HEART_RATE_ALERTS = "heart_rate_alerts"
        const val CHANNEL_HEALTH_RECOMMENDATIONS = "health_recommendations"
        const val CHANNEL_DEVICE_STATUS = "device_status"
        const val CHANNEL_DAILY_SUMMARY = "daily_summary"
        
        // Notification IDs
        const val NOTIFICATION_SMOKING_DETECTED = 2001
        const val NOTIFICATION_HEART_RATE_ALERT = 2002
        const val NOTIFICATION_HEALTH_TIP = 2003
        const val NOTIFICATION_DEVICE_DISCONNECTED = 2004
        const val NOTIFICATION_DAILY_SUMMARY = 2005
        const val NOTIFICATION_SMOKING_GOAL = 2006
        const val NOTIFICATION_HEART_RATE_ZONE = 2007
        const val NOTIFICATION_WEEKLY_REPORT = 2008
    }
    
    init {
        createNotificationChannels()
    }
    
    /**
     * Creates all notification channels
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_SMOKING_ALERTS,
                    "Smoking Alerts",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Notifications for detected smoking events"
                    enableVibration(true)
                    setShowBadge(true)
                },
                
                NotificationChannel(
                    CHANNEL_HEART_RATE_ALERTS,
                    "Heart Rate Alerts",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Notifications for heart rate anomalies"
                    enableVibration(true)
                    setShowBadge(true)
                },
                
                NotificationChannel(
                    CHANNEL_HEALTH_RECOMMENDATIONS,
                    "Health Recommendations",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Health tips and recommendations"
                    setShowBadge(false)
                },
                
                NotificationChannel(
                    CHANNEL_DEVICE_STATUS,
                    "Device Status",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Wearable device connection status"
                    setShowBadge(false)
                },
                
                NotificationChannel(
                    CHANNEL_DAILY_SUMMARY,
                    "Daily Summary",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Daily health summaries and reports"
                    setShowBadge(true)
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { systemNotificationManager.createNotificationChannel(it) }
        }
    }
    
    /**
     * Shows smoking detection notification
     */
    fun showSmokingDetectedNotification(
        smokingEvent: SmokingEvent,
        confidence: Float
    ) {
        val mainIntent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val confidencePercent = (confidence * 100).toInt()
        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
        val timeString = timeFormat.format(Date(smokingEvent.timestamp))
        
        val notification = NotificationCompat.Builder(context, CHANNEL_SMOKING_ALERTS)
            .setContentTitle("Smoking Event Detected")
            .setContentText("Detected at $timeString with $confidencePercent% confidence")
            .setSmallIcon(R.drawable.ic_smoking_detected)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("Smoking event detected at $timeString via ${smokingEvent.detectionMethod} with $confidencePercent% confidence. Tap to view details."))
            .build()
        
        notificationManager.notify(NOTIFICATION_SMOKING_DETECTED, notification)
    }
    
    /**
     * Shows heart rate alert notification
     */
    fun showHeartRateAlert(
        heartRateData: HeartRateData,
        alertType: HeartRateAlertType,
        message: String
    ) {
        val mainIntent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val title = when (alertType) {
            HeartRateAlertType.HIGH -> "High Heart Rate Alert"
            HeartRateAlertType.LOW -> "Low Heart Rate Alert"
            HeartRateAlertType.IRREGULAR -> "Irregular Heart Rate"
            HeartRateAlertType.SMOKING_RELATED -> "Smoking Impact Alert"
        }
        
        val icon = when (alertType) {
            HeartRateAlertType.HIGH -> R.drawable.ic_heart_rate_high
            HeartRateAlertType.LOW -> R.drawable.ic_heart_rate_low
            HeartRateAlertType.IRREGULAR -> R.drawable.ic_heart_rate_irregular
            HeartRateAlertType.SMOKING_RELATED -> R.drawable.ic_smoking_heart_alert
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_HEART_RATE_ALERTS)
            .setContentTitle(title)
            .setContentText("${heartRateData.heartRate} BPM - $message")
            .setSmallIcon(icon)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
            .build()
        
        notificationManager.notify(NOTIFICATION_HEART_RATE_ALERT, notification)
    }
    
    /**
     * Shows health recommendation notification
     */
    fun showHealthRecommendation(
        title: String,
        message: String,
        recommendationType: HealthRecommendationType
    ) {
        val mainIntent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val icon = when (recommendationType) {
            HealthRecommendationType.SMOKING_CESSATION -> R.drawable.ic_no_smoking
            HealthRecommendationType.EXERCISE -> R.drawable.ic_exercise
            HealthRecommendationType.STRESS_MANAGEMENT -> R.drawable.ic_stress_relief
            HealthRecommendationType.GENERAL_HEALTH -> R.drawable.ic_health_tip
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_HEALTH_RECOMMENDATIONS)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(icon)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .build()
        
        notificationManager.notify(NOTIFICATION_HEALTH_TIP, notification)
    }
    
    /**
     * Shows device disconnection notification
     */
    fun showDeviceDisconnectedNotification(deviceName: String) {
        val mainIntent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_DEVICE_STATUS)
            .setContentTitle("Device Disconnected")
            .setContentText("$deviceName has been disconnected")
            .setSmallIcon(R.drawable.ic_device_disconnected)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(NOTIFICATION_DEVICE_DISCONNECTED, notification)
    }
    
    /**
     * Shows daily summary notification
     */
    fun showDailySummaryNotification(
        smokingCount: Int,
        avgHeartRate: Int,
        recommendations: List<String>
    ) {
        val mainIntent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, mainIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val summaryText = buildString {
            append("Today: $smokingCount smoking events")
            if (avgHeartRate > 0) {
                append(", Average HR: $avgHeartRate BPM")
            }
        }
        
        val bigText = buildString {
            append(summaryText)
            if (recommendations.isNotEmpty()) {
                append("\n\nRecommendations:\n")
                recommendations.take(3).forEach { append("• $it\n") }
            }
        }
        
        val notification = NotificationCompat.Builder(context, CHANNEL_DAILY_SUMMARY)
            .setContentTitle("Daily Health Summary")
            .setContentText(summaryText)
            .setSmallIcon(R.drawable.ic_daily_summary)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setStyle(NotificationCompat.BigTextStyle().bigText(bigText))
            .build()
        
        notificationManager.notify(NOTIFICATION_DAILY_SUMMARY, notification)
    }
    
    /**
     * Cancels a specific notification
     */
    fun cancelNotification(notificationId: Int) {
        notificationManager.cancel(notificationId)
    }
    
    /**
     * Cancels all notifications
     */
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
    }
}

/**
 * Types of heart rate alerts
 */
enum class HeartRateAlertType {
    HIGH, LOW, IRREGULAR, SMOKING_RELATED
}

/**
 * Types of health recommendations
 */
enum class HealthRecommendationType {
    SMOKING_CESSATION, EXERCISE, STRESS_MANAGEMENT, GENERAL_HEALTH
}
