package com.thryve.exhale.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.thryve.exhale.R
import com.thryve.exhale.sensor.SensorManager
import com.thryve.exhale.data.entity.SmokingEvent
import com.thryve.exhale.detection.SmokingDetectionEngine
import com.thryve.exhale.detection.DetectionSensitivity
import com.thryve.exhale.detection.SmokingDetectionResult
import com.thryve.exhale.notification.ExhaleNotificationManager
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import java.util.Date


/**
 * Background service for continuous smoking detection using device sensors
 */
class SmokingDetectionService : LifecycleService() {
    
    // TODO: Add dependency injection later
    private lateinit var sensorManager: SensorManager
    private lateinit var smokingDetectionEngine: SmokingDetectionEngine
    private lateinit var notificationManager: ExhaleNotificationManager
    
    private val binder = SmokingDetectionServiceBinder()
    private var isDetecting = false
    private var detectionCount = 0
    private var lastDetectionTime = 0L
    
    companion object {
        private const val TAG = "SmokingDetectionService"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "smoking_detection"
        
        const val ACTION_START_DETECTION = "START_DETECTION"
        const val ACTION_STOP_DETECTION = "STOP_DETECTION"
        const val ACTION_MANUAL_LOG = "MANUAL_LOG"
    }
    
    inner class SmokingDetectionServiceBinder : Binder() {
        fun getService(): SmokingDetectionService = this@SmokingDetectionService
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        notificationManager = ExhaleNotificationManager(this)
        Log.i(TAG, "Smoking Detection Service created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        
        when (intent?.action) {
            ACTION_START_DETECTION -> startDetection()
            ACTION_STOP_DETECTION -> stopDetection()
            ACTION_MANUAL_LOG -> logManualSmokingEvent()
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }
    
    /**
     * Starts smoking detection
     */
    fun startDetection(sensitivity: DetectionSensitivity = DetectionSensitivity.MEDIUM) {
        if (isDetecting) {
            Log.w(TAG, "Smoking detection already started")
            return
        }
        
        isDetecting = true
        startForeground(NOTIFICATION_ID, createNotification("Starting smoking detection..."))
        
        // Check sensor availability
        val sensorAvailability = sensorManager.checkSensorAvailability()
        if (!sensorAvailability.hasRequiredSensors) {
            Log.e(TAG, "Required sensors not available")
            stopDetection()
            return
        }
        
        // Start sensor collection
        sensorManager.startCollection()
        
        // Start detection engine
        smokingDetectionEngine.startDetection(sensitivity)
        
        // Observe sensor data and feed to detection engine
        lifecycleScope.launch {
            sensorManager.accelerometerData.collect { sensorData ->
                smokingDetectionEngine.processSensorData(sensorData)
            }
        }
        
        lifecycleScope.launch {
            sensorManager.gyroscopeData.collect { sensorData ->
                smokingDetectionEngine.processSensorData(sensorData)
            }
        }
        
        lifecycleScope.launch {
            sensorManager.magnetometerData.collect { sensorData ->
                smokingDetectionEngine.processSensorData(sensorData)
            }
        }
        
        // Observe smoking detection results
        lifecycleScope.launch {
            smokingDetectionEngine.smokingDetected.collect { result ->
                handleSmokingDetection(result)
            }
        }
        
        updateNotification("Smoking detection active")
        Log.i(TAG, "Smoking detection started with sensitivity: $sensitivity")
    }
    
    /**
     * Stops smoking detection
     */
    fun stopDetection() {
        if (!isDetecting) return
        
        isDetecting = false
        sensorManager.stopCollection()
        smokingDetectionEngine.stopDetection()
        
        stopForeground(true)
        stopSelf()
        
        Log.i(TAG, "Smoking detection stopped")
    }
    
    /**
     * Handles detected smoking events
     */
    private suspend fun handleSmokingDetection(result: SmokingDetectionResult) {
        detectionCount++
        lastDetectionTime = System.currentTimeMillis()
        
        // Send notification
        sendSmokingDetectionNotification(result)
        
        // Update service notification
        updateNotification("Smoking detected! Count today: $detectionCount")
        
        Log.i(TAG, "Smoking event detected with confidence: ${result.confidence}")
    }
    
    /**
     * Logs a manual smoking event
     */
    private fun logManualSmokingEvent() {
        lifecycleScope.launch {
            try {
                val eventId = smokingDetectionEngine.manualSmokingEvent("Manually logged by user")
                detectionCount++
                
                sendManualLogNotification()
                updateNotification("Manual smoking event logged. Count today: $detectionCount")
                
                Log.i(TAG, "Manual smoking event logged with ID: $eventId")
            } catch (e: Exception) {
                Log.e(TAG, "Error logging manual smoking event", e)
            }
        }
    }
    
    /**
     * Sends smoking detection notification
     */
    private fun sendSmokingDetectionNotification(result: SmokingDetectionResult) {
        // Create smoking event for the notification
        val smokingEvent = SmokingEvent(
            startTime = result.timestamp,
            confidence = result.confidence,
            detectionMethod = result.detectionMethod,
            isConfirmed = false
        )

        // Use the new notification manager
        notificationManager.showSmokingDetectedNotification(smokingEvent, result.confidence)
    }
    
    /**
     * Sends manual log confirmation notification
     */
    private fun sendManualLogNotification() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Smoking Event Logged")
            .setContentText("Manual smoking event has been recorded")
            .setSmallIcon(R.drawable.ic_manual_log)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID + 2, notification)
    }
    
    /**
     * Creates notification channel for Android O+
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "Smoking Detection",
            NotificationManager.IMPORTANCE_DEFAULT
        ).apply {
            description = "Notifications for smoking detection events"
            setShowBadge(true)
        }
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * Creates foreground service notification
     */
    private fun createNotification(contentText: String): Notification {
        val stopIntent = Intent(this, SmokingDetectionService::class.java).apply {
            action = ACTION_STOP_DETECTION
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val manualLogIntent = Intent(this, SmokingDetectionService::class.java).apply {
            action = ACTION_MANUAL_LOG
        }
        val manualLogPendingIntent = PendingIntent.getService(
            this, 1, manualLogIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Smoking Detection")
            .setContentText(contentText)
            .setSmallIcon(R.drawable.ic_smoking_detection)
            .setOngoing(true)
            .addAction(R.drawable.ic_add, "Log Smoking", manualLogPendingIntent)
            .addAction(R.drawable.ic_stop, "Stop", stopPendingIntent)
            .build()
    }
    
    /**
     * Updates the foreground notification
     */
    private fun updateNotification(contentText: String) {
        val notification = createNotification(contentText)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * Gets current detection status
     */
    fun getDetectionStatus(): SmokingDetectionStatus {
        return SmokingDetectionStatus(
            isDetecting = isDetecting,
            detectionCount = detectionCount,
            lastDetectionTime = if (lastDetectionTime > 0) java.util.Date(lastDetectionTime) else null,
            sensorAvailability = sensorManager.checkSensorAvailability(),
            detectionStats = smokingDetectionEngine.getDetectionStats()
        )
    }
    
    /**
     * Updates detection sensitivity
     */
    fun updateSensitivity(sensitivity: DetectionSensitivity) {
        smokingDetectionEngine.updateSensitivity(sensitivity)
        updateNotification("Detection sensitivity updated to $sensitivity")
        Log.i(TAG, "Detection sensitivity updated to: $sensitivity")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopDetection()
        sensorManager.cleanup()
        smokingDetectionEngine.cleanup()
        Log.i(TAG, "Smoking Detection Service destroyed")
    }
}

/**
 * Status information for smoking detection
 */
data class SmokingDetectionStatus(
    val isDetecting: Boolean,
    val detectionCount: Int,
    val lastDetectionTime: java.util.Date?,
    val sensorAvailability: com.thryve.exhale.sensor.SensorAvailability,
    val detectionStats: com.thryve.exhale.detection.DetectionStats
)
