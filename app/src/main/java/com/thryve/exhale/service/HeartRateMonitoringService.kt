package com.thryve.exhale.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.thryve.exhale.R
import com.thryve.exhale.bluetooth.BleManager
import com.thryve.exhale.data.entity.HeartRateData
import com.thryve.exhale.data.repository.HeartRateRepository
import com.thryve.exhale.detection.SmokingDetectionEngine
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

/**
 * Background service for continuous heart rate monitoring from wearable devices
 */
class HeartRateMonitoringService : LifecycleService() {
    
    @Inject
    lateinit var bleManager: BleManager
    
    @Inject
    lateinit var heartRateRepository: HeartRateRepository
    
    @Inject
    lateinit var smokingDetectionEngine: SmokingDetectionEngine
    
    private val binder = HeartRateServiceBinder()
    private var isMonitoring = false
    private var lastHeartRate = 0
    private var lastHeartRateTime = 0L
    
    // Heart rate analysis
    private val heartRateHistory = mutableListOf<HeartRateData>()
    private var baselineHeartRate = 0
    private var isCalculatingBaseline = true
    
    companion object {
        private const val TAG = "HeartRateMonitoringService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "heart_rate_monitoring"
        private const val BASELINE_CALCULATION_PERIOD = 300000L // 5 minutes
        private const val HEART_RATE_TIMEOUT = 30000L // 30 seconds
        
        const val ACTION_START_MONITORING = "START_MONITORING"
        const val ACTION_STOP_MONITORING = "STOP_MONITORING"
    }
    
    inner class HeartRateServiceBinder : Binder() {
        fun getService(): HeartRateMonitoringService = this@HeartRateMonitoringService
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        Log.i(TAG, "Heart Rate Monitoring Service created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        
        when (intent?.action) {
            ACTION_START_MONITORING -> startMonitoring()
            ACTION_STOP_MONITORING -> stopMonitoring()
        }
        
        return START_STICKY
    }
    
    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        return binder
    }
    
    /**
     * Starts heart rate monitoring
     */
    fun startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "Heart rate monitoring already started")
            return
        }
        
        isMonitoring = true
        startForeground(NOTIFICATION_ID, createNotification("Starting heart rate monitoring..."))
        
        // Start BLE scanning and connection
        if (bleManager.isBluetoothAvailable()) {
            bleManager.startScanning()
        } else {
            Log.w(TAG, "Bluetooth not available")
            stopMonitoring()
            return
        }
        
        // Observe heart rate data
        lifecycleScope.launch {
            bleManager.heartRateReceived.collect { heartRateData ->
                handleHeartRateData(heartRateData)
            }
        }
        
        // Observe device connections
        lifecycleScope.launch {
            bleManager.deviceConnected.collect { device ->
                updateNotification("Connected to ${device.deviceName}")
                Log.i(TAG, "Connected to heart rate device: ${device.deviceName}")
            }
        }
        
        // Observe device disconnections
        lifecycleScope.launch {
            bleManager.deviceDisconnected.collect { deviceAddress ->
                updateNotification("Device disconnected")
                Log.i(TAG, "Heart rate device disconnected: $deviceAddress")
            }
        }
        
        Log.i(TAG, "Heart rate monitoring started")
    }
    
    /**
     * Stops heart rate monitoring
     */
    fun stopMonitoring() {
        if (!isMonitoring) return
        
        isMonitoring = false
        bleManager.disconnectAllDevices()
        bleManager.stopScanning()
        
        stopForeground(true)
        stopSelf()
        
        Log.i(TAG, "Heart rate monitoring stopped")
    }
    
    /**
     * Handles incoming heart rate data
     */
    private suspend fun handleHeartRateData(heartRateData: HeartRateData) {
        lastHeartRate = heartRateData.heartRate
        lastHeartRateTime = System.currentTimeMillis()
        
        // Add to history for analysis
        heartRateHistory.add(heartRateData)
        
        // Keep only recent data (last 10 minutes)
        val cutoffTime = Date(System.currentTimeMillis() - 600000L)
        heartRateHistory.removeAll { it.timestamp.before(cutoffTime) }
        
        // Calculate baseline if needed
        if (isCalculatingBaseline) {
            calculateBaseline()
        }
        
        // Analyze heart rate patterns
        analyzeHeartRatePatterns(heartRateData)
        
        // Update notification
        updateNotification("Heart Rate: ${heartRateData.heartRate} BPM")
        
        Log.d(TAG, "Heart rate: ${heartRateData.heartRate} BPM from ${heartRateData.deviceType}")
    }
    
    /**
     * Calculates baseline heart rate
     */
    private fun calculateBaseline() {
        if (heartRateHistory.size < 10) return
        
        val recentData = heartRateHistory.takeLast(20)
        val averageHeartRate = recentData.map { it.heartRate }.average().toInt()
        
        // Only update baseline if we have stable readings
        val variance = recentData.map { (it.heartRate - averageHeartRate).let { diff -> diff * diff } }.average()
        
        if (variance < 100) { // Low variance indicates stable readings
            baselineHeartRate = averageHeartRate
            
            // Stop calculating baseline after initial period
            if (System.currentTimeMillis() - (heartRateHistory.firstOrNull()?.timestamp?.time ?: 0L) > BASELINE_CALCULATION_PERIOD) {
                isCalculatingBaseline = false
                Log.i(TAG, "Baseline heart rate established: $baselineHeartRate BPM")
            }
        }
    }
    
    /**
     * Analyzes heart rate patterns for anomalies
     */
    private suspend fun analyzeHeartRatePatterns(heartRateData: HeartRateData) {
        if (baselineHeartRate == 0) return
        
        val currentHeartRate = heartRateData.heartRate
        val deviation = currentHeartRate - baselineHeartRate
        
        // Check for significant heart rate changes
        when {
            deviation > 30 -> {
                Log.w(TAG, "Elevated heart rate detected: $currentHeartRate BPM (baseline: $baselineHeartRate)")
                // Could indicate stress, exercise, or smoking
                checkForSmokingCorrelation(heartRateData)
            }
            deviation < -20 -> {
                Log.w(TAG, "Low heart rate detected: $currentHeartRate BPM (baseline: $baselineHeartRate)")
            }
            currentHeartRate > 120 -> {
                Log.w(TAG, "High heart rate alert: $currentHeartRate BPM")
                sendHeartRateAlert("High heart rate detected: $currentHeartRate BPM")
            }
            currentHeartRate < 50 -> {
                Log.w(TAG, "Low heart rate alert: $currentHeartRate BPM")
                sendHeartRateAlert("Low heart rate detected: $currentHeartRate BPM")
            }
        }
        
        // Update heart rate data with baseline information
        val updatedData = heartRateData.copy(
            metadata = """{"baseline": $baselineHeartRate, "deviation": $deviation}"""
        )
        heartRateRepository.updateHeartRateData(updatedData)
    }
    
    /**
     * Checks if heart rate changes correlate with smoking events
     */
    private suspend fun checkForSmokingCorrelation(heartRateData: HeartRateData) {
        // Get recent smoking events
        val recentEvents = smokingDetectionEngine.getDetectionStats().currentEvent
        
        if (recentEvents != null) {
            // Update smoking event with heart rate data
            Log.i(TAG, "Correlating heart rate with ongoing smoking event")
            
            // This could trigger additional analysis or notifications
            sendSmokingHeartRateAlert(heartRateData.heartRate)
        }
    }
    
    /**
     * Sends heart rate alert notification
     */
    private fun sendHeartRateAlert(message: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Heart Rate Alert")
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_heart_rate)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID + 1, notification)
    }
    
    /**
     * Sends smoking-related heart rate alert
     */
    private fun sendSmokingHeartRateAlert(heartRate: Int) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Smoking & Heart Rate Alert")
            .setContentText("Heart rate elevated to $heartRate BPM during smoking event")
            .setSmallIcon(R.drawable.ic_smoking_alert)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID + 2, notification)
    }
    
    /**
     * Creates notification channel for Android O+
     */
    private fun createNotificationChannel() {
        val channel = NotificationChannel(
            CHANNEL_ID,
            "Heart Rate Monitoring",
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "Continuous heart rate monitoring from wearable devices"
            setShowBadge(false)
        }
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.createNotificationChannel(channel)
    }
    
    /**
     * Creates foreground service notification
     */
    private fun createNotification(contentText: String): Notification {
        val stopIntent = Intent(this, HeartRateMonitoringService::class.java).apply {
            action = ACTION_STOP_MONITORING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Heart Rate Monitoring")
            .setContentText(contentText)
            .setSmallIcon(R.drawable.ic_heart_rate)
            .setOngoing(true)
            .addAction(R.drawable.ic_stop, "Stop", stopPendingIntent)
            .build()
    }
    
    /**
     * Updates the foreground notification
     */
    private fun updateNotification(contentText: String) {
        val notification = createNotification(contentText)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * Gets current heart rate monitoring status
     */
    fun getMonitoringStatus(): HeartRateMonitoringStatus {
        return HeartRateMonitoringStatus(
            isMonitoring = isMonitoring,
            lastHeartRate = lastHeartRate,
            lastHeartRateTime = if (lastHeartRateTime > 0) Date(lastHeartRateTime) else null,
            baselineHeartRate = baselineHeartRate,
            connectedDevices = bleManager.getConnectedDeviceAddresses(),
            isCalculatingBaseline = isCalculatingBaseline
        )
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopMonitoring()
        bleManager.cleanup()
        Log.i(TAG, "Heart Rate Monitoring Service destroyed")
    }
}

/**
 * Status information for heart rate monitoring
 */
data class HeartRateMonitoringStatus(
    val isMonitoring: Boolean,
    val lastHeartRate: Int,
    val lastHeartRateTime: Date?,
    val baselineHeartRate: Int,
    val connectedDevices: List<String>,
    val isCalculatingBaseline: Boolean
)
