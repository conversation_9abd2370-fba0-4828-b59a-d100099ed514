package com.thryve.exhale.health

import com.thryve.exhale.data.model.HeartRateData
import com.thryve.exhale.data.model.SmokingEvent
import com.thryve.exhale.notification.HealthRecommendationType
import java.util.*
import kotlin.random.Random

/**
 * Engine for generating personalized health recommendations
 */
class HealthRecommendationEngine {
    
    companion object {
        private const val HIGH_HEART_RATE_THRESHOLD = 100
        private const val LOW_HEART_RATE_THRESHOLD = 60
        private const val SMOKING_FREQUENCY_HIGH = 10 // per day
        private const val SMOKING_FREQUENCY_MODERATE = 5 // per day
    }
    
    /**
     * Generates recommendations based on smoking patterns and heart rate data
     */
    fun generateRecommendations(
        recentSmokingEvents: List<SmokingEvent>,
        recentHeartRateData: List<HeartRateData>,
        userAge: Int? = null,
        userWeight: Double? = null
    ): List<HealthRecommendation> {
        val recommendations = mutableListOf<HealthRecommendation>()
        
        // Analyze smoking patterns
        val dailySmokingCount = recentSmokingEvents.count { 
            isToday(Date(it.timestamp)) 
        }
        val weeklySmokingCount = recentSmokingEvents.count { 
            isThisWeek(Date(it.timestamp)) 
        }
        
        // Analyze heart rate patterns
        val avgHeartRate = recentHeartRateData.map { it.heartRate }.average()
        val maxHeartRate = recentHeartRateData.maxOfOrNull { it.heartRate } ?: 0
        
        // Generate smoking-related recommendations
        recommendations.addAll(generateSmokingRecommendations(dailySmokingCount, weeklySmokingCount))
        
        // Generate heart rate recommendations
        recommendations.addAll(generateHeartRateRecommendations(avgHeartRate, maxHeartRate, userAge))
        
        // Generate correlation-based recommendations
        recommendations.addAll(generateCorrelationRecommendations(recentSmokingEvents, recentHeartRateData))
        
        // Generate general health recommendations
        recommendations.addAll(generateGeneralHealthRecommendations(dailySmokingCount, avgHeartRate))
        
        return recommendations.distinctBy { it.title }.take(5) // Limit to 5 unique recommendations
    }
    
    private fun generateSmokingRecommendations(dailyCount: Int, weeklyCount: Int): List<HealthRecommendation> {
        val recommendations = mutableListOf<HealthRecommendation>()
        
        when {
            dailyCount >= SMOKING_FREQUENCY_HIGH -> {
                recommendations.add(
                    HealthRecommendation(
                        title = "Consider Smoking Cessation Support",
                        message = "You've smoked $dailyCount times today. Consider reaching out to a smoking cessation program or your healthcare provider for support.",
                        type = HealthRecommendationType.SMOKING_CESSATION,
                        priority = RecommendationPriority.HIGH
                    )
                )
                recommendations.add(
                    HealthRecommendation(
                        title = "Try Nicotine Replacement Therapy",
                        message = "Nicotine patches, gum, or lozenges can help reduce cravings and withdrawal symptoms.",
                        type = HealthRecommendationType.SMOKING_CESSATION,
                        priority = RecommendationPriority.MEDIUM
                    )
                )
            }
            dailyCount >= SMOKING_FREQUENCY_MODERATE -> {
                recommendations.add(
                    HealthRecommendation(
                        title = "Gradual Reduction Strategy",
                        message = "Try reducing your daily smoking by 1-2 cigarettes. Small steps lead to big changes!",
                        type = HealthRecommendationType.SMOKING_CESSATION,
                        priority = RecommendationPriority.MEDIUM
                    )
                )
            }
            dailyCount > 0 -> {
                recommendations.add(
                    HealthRecommendation(
                        title = "Identify Your Triggers",
                        message = "Notice what situations or emotions trigger your smoking. This awareness is the first step to quitting.",
                        type = HealthRecommendationType.SMOKING_CESSATION,
                        priority = RecommendationPriority.LOW
                    )
                )
            }
        }
        
        return recommendations
    }
    
    private fun generateHeartRateRecommendations(avgHeartRate: Double, maxHeartRate: Int, userAge: Int?): List<HealthRecommendation> {
        val recommendations = mutableListOf<HealthRecommendation>()
        
        when {
            avgHeartRate > HIGH_HEART_RATE_THRESHOLD -> {
                recommendations.add(
                    HealthRecommendation(
                        title = "High Heart Rate Detected",
                        message = "Your average heart rate is ${avgHeartRate.toInt()} BPM. Consider stress management techniques and consult your doctor.",
                        type = HealthRecommendationType.STRESS_MANAGEMENT,
                        priority = RecommendationPriority.HIGH
                    )
                )
                recommendations.add(
                    HealthRecommendation(
                        title = "Try Deep Breathing Exercises",
                        message = "Practice 4-7-8 breathing: Inhale for 4, hold for 7, exhale for 8. This can help lower your heart rate.",
                        type = HealthRecommendationType.STRESS_MANAGEMENT,
                        priority = RecommendationPriority.MEDIUM
                    )
                )
            }
            avgHeartRate < LOW_HEART_RATE_THRESHOLD && userAge != null && userAge > 65 -> {
                recommendations.add(
                    HealthRecommendation(
                        title = "Low Heart Rate Monitoring",
                        message = "Your heart rate is lower than normal. If you experience dizziness or fatigue, consult your healthcare provider.",
                        type = HealthRecommendationType.GENERAL_HEALTH,
                        priority = RecommendationPriority.MEDIUM
                    )
                )
            }
        }
        
        return recommendations
    }
    
    private fun generateCorrelationRecommendations(
        smokingEvents: List<SmokingEvent>,
        heartRateData: List<HeartRateData>
    ): List<HealthRecommendation> {
        val recommendations = mutableListOf<HealthRecommendation>()
        
        // Check if heart rate spikes correlate with smoking events
        val smokingTimes = smokingEvents.map { it.timestamp }
        val heartRateSpikes = heartRateData.filter { it.heartRate > HIGH_HEART_RATE_THRESHOLD }
        
        val correlatedEvents = heartRateSpikes.count { hrData ->
            smokingTimes.any { smokingTime ->
                Math.abs(hrData.timestamp - smokingTime) < 30 * 60 * 1000 // Within 30 minutes
            }
        }
        
        if (correlatedEvents > 0 && heartRateSpikes.isNotEmpty()) {
            val correlationPercentage = (correlatedEvents.toDouble() / heartRateSpikes.size * 100).toInt()
            recommendations.add(
                HealthRecommendation(
                    title = "Smoking Impact on Heart Rate",
                    message = "$correlationPercentage% of your heart rate spikes occur near smoking events. Quitting could improve your cardiovascular health.",
                    type = HealthRecommendationType.SMOKING_CESSATION,
                    priority = RecommendationPriority.HIGH
                )
            )
        }
        
        return recommendations
    }
    
    private fun generateGeneralHealthRecommendations(dailySmokingCount: Int, avgHeartRate: Double): List<HealthRecommendation> {
        val recommendations = mutableListOf<HealthRecommendation>()
        
        // Exercise recommendations
        if (avgHeartRate < 80 && dailySmokingCount < SMOKING_FREQUENCY_MODERATE) {
            recommendations.add(
                HealthRecommendation(
                    title = "Increase Physical Activity",
                    message = "Your heart rate suggests you could benefit from more cardio exercise. Try 30 minutes of brisk walking daily.",
                    type = HealthRecommendationType.EXERCISE,
                    priority = RecommendationPriority.LOW
                )
            )
        }
        
        // Hydration and nutrition
        recommendations.add(
            HealthRecommendation(
                title = "Stay Hydrated",
                message = "Proper hydration supports cardiovascular health and can help reduce smoking cravings.",
                type = HealthRecommendationType.GENERAL_HEALTH,
                priority = RecommendationPriority.LOW
            )
        )
        
        // Sleep recommendations
        if (Random.nextBoolean()) { // Randomly include sleep advice
            recommendations.add(
                HealthRecommendation(
                    title = "Prioritize Quality Sleep",
                    message = "7-9 hours of quality sleep can improve heart health and reduce stress-related smoking triggers.",
                    type = HealthRecommendationType.GENERAL_HEALTH,
                    priority = RecommendationPriority.LOW
                )
            )
        }
        
        return recommendations
    }
    
    /**
     * Generates motivational messages for smoking cessation
     */
    fun generateMotivationalMessage(daysSinceLastSmoke: Int): String {
        return when {
            daysSinceLastSmoke == 0 -> "Every moment without smoking is a victory. You can do this!"
            daysSinceLastSmoke == 1 -> "Congratulations on your first smoke-free day! Your body is already starting to heal."
            daysSinceLastSmoke < 7 -> "Day $daysSinceLastSmoke smoke-free! Your heart rate and blood pressure are improving."
            daysSinceLastSmoke < 30 -> "Amazing! $daysSinceLastSmoke days smoke-free. Your circulation is improving and your risk of heart attack is decreasing."
            daysSinceLastSmoke < 365 -> "Incredible progress! $daysSinceLastSmoke days smoke-free. Your lung function is significantly improved."
            else -> "Outstanding! Over a year smoke-free. You've dramatically reduced your risk of heart disease and cancer."
        }
    }
    
    private fun isToday(date: Date): Boolean {
        val today = Calendar.getInstance()
        val targetDate = Calendar.getInstance().apply { time = date }
        return today.get(Calendar.YEAR) == targetDate.get(Calendar.YEAR) &&
                today.get(Calendar.DAY_OF_YEAR) == targetDate.get(Calendar.DAY_OF_YEAR)
    }
    
    private fun isThisWeek(date: Date): Boolean {
        val now = Calendar.getInstance()
        val weekAgo = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -7)
        }
        return date.after(weekAgo.time) && date.before(now.time)
    }
}

/**
 * Data class for health recommendations
 */
data class HealthRecommendation(
    val title: String,
    val message: String,
    val type: HealthRecommendationType,
    val priority: RecommendationPriority,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Priority levels for recommendations
 */
enum class RecommendationPriority {
    LOW, MEDIUM, HIGH
}
