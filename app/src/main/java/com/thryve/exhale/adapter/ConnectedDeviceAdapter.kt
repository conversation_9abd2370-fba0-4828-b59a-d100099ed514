package com.thryve.exhale.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.thryve.exhale.R
import com.thryve.exhale.data.entity.ConnectedDevice
import com.thryve.exhale.databinding.ItemConnectedDeviceBinding
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for displaying connected devices in RecyclerView
 */
class ConnectedDeviceAdapter(
    private val onDeviceClick: (ConnectedDevice) -> Unit
) : RecyclerView.Adapter<ConnectedDeviceAdapter.DeviceViewHolder>() {
    
    private val devices = mutableListOf<ConnectedDevice>()
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val binding = ItemConnectedDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DeviceViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON><PERSON>wHold<PERSON>, position: Int) {
        holder.bind(devices[position])
    }
    
    override fun getItemCount(): Int = devices.size
    
    fun addDevice(device: ConnectedDevice) {
        // Check if device already exists
        val existingIndex = devices.indexOfFirst { it.deviceId == device.deviceId }
        if (existingIndex != -1) {
            // Update existing device
            devices[existingIndex] = device
            notifyItemChanged(existingIndex)
        } else {
            // Add new device
            devices.add(device)
            notifyItemInserted(devices.size - 1)
        }
    }
    
    fun removeDevice(deviceAddress: String) {
        val index = devices.indexOfFirst { it.macAddress == deviceAddress }
        if (index != -1) {
            devices.removeAt(index)
            notifyItemRemoved(index)
        }
    }
    
    fun updateDevice(device: ConnectedDevice) {
        val index = devices.indexOfFirst { it.deviceId == device.deviceId }
        if (index != -1) {
            devices[index] = device
            notifyItemChanged(index)
        }
    }
    
    fun clearDevices() {
        val size = devices.size
        devices.clear()
        notifyItemRangeRemoved(0, size)
    }
    
    inner class DeviceViewHolder(
        private val binding: ItemConnectedDeviceBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(device: ConnectedDevice) {
            binding.apply {
                tvDeviceName.text = device.deviceName
                
                // Set device icon based on type
                val iconRes = when {
                    device.deviceType.contains("Apple Watch", ignoreCase = true) -> R.drawable.ic_heart_rate
                    device.deviceType.contains("Galaxy Watch", ignoreCase = true) -> R.drawable.ic_heart_rate
                    device.deviceType.contains("Fitbit", ignoreCase = true) -> R.drawable.ic_heart_rate
                    device.deviceType.contains("Garmin", ignoreCase = true) -> R.drawable.ic_heart_rate
                    else -> R.drawable.ic_heart_rate
                }
                ivDeviceIcon.setImageResource(iconRes)
                
                // Format device info
                val batteryInfo = if (device.batteryLevel != null && device.batteryLevel > 0) {
                    "Battery: ${device.batteryLevel}%"
                } else {
                    "Battery: Unknown"
                }
                
                val connectionInfo = if (device.isConnected) {
                    "Connected"
                } else {
                    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                    "Last seen: ${timeFormat.format(device.lastSeen)}"
                }
                
                tvDeviceInfo.text = "$connectionInfo • $batteryInfo"
                
                // Update connection status
                if (device.isConnected) {
                    tvConnectionStatus.text = "Connected"
                    tvConnectionStatus.setBackgroundResource(R.drawable.status_indicator_connected)
                    tvConnectionStatus.setTextColor(ContextCompat.getColor(root.context, android.R.color.white))
                } else {
                    tvConnectionStatus.text = "Disconnected"
                    tvConnectionStatus.setBackgroundResource(R.drawable.status_indicator_disconnected)
                    tvConnectionStatus.setTextColor(ContextCompat.getColor(root.context, R.color.status_disconnected))
                }
                
                // Set click listener
                root.setOnClickListener {
                    onDeviceClick(device)
                }
            }
        }
    }
}
