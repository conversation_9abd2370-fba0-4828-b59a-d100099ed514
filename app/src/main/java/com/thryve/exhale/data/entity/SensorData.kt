package com.thryve.exhale.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Entity for storing raw sensor data used in smoking detection
 */
@Entity(tableName = "sensor_data")
@Parcelize
data class SensorData(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * Type of sensor that generated this data
     */
    val sensorType: SensorType,
    
    /**
     * Timestamp when data was collected
     */
    val timestamp: Date,
    
    /**
     * X-axis value
     */
    val x: Float,
    
    /**
     * Y-axis value
     */
    val y: Float,
    
    /**
     * Z-axis value
     */
    val z: Float,
    
    /**
     * Sensor accuracy level
     */
    val accuracy: Int,
    
    /**
     * Whether this data point contributed to smoking detection
     */
    val isPartOfSmokingEvent: Boolean = false,
    
    /**
     * Associated smoking event ID if applicable
     */
    val smokingEventId: Long? = null,
    
    /**
     * Processed/filtered values if applicable
     */
    val processedX: Float? = null,
    val processedY: Float? = null,
    val processedZ: Float? = null,
    
    /**
     * Magnitude of the sensor vector
     */
    val magnitude: Float = kotlin.math.sqrt(x * x + y * y + z * z)
) : Parcelable

/**
 * Types of sensors used for smoking detection
 */
enum class SensorType {
    ACCELEROMETER,
    GYROSCOPE,
    MAGNETOMETER,
    LINEAR_ACCELERATION,
    GRAVITY,
    ROTATION_VECTOR
}

/**
 * Entity for storing device information
 */
@Entity(tableName = "connected_devices")
@Parcelize
data class ConnectedDevice(
    @PrimaryKey
    val deviceId: String,
    
    /**
     * Human-readable device name
     */
    val deviceName: String,
    
    /**
     * Device type/model
     */
    val deviceType: String,
    
    /**
     * Bluetooth MAC address
     */
    val macAddress: String,
    
    /**
     * When device was first connected
     */
    val firstConnected: Date,
    
    /**
     * Last time device was seen/connected
     */
    val lastSeen: Date,
    
    /**
     * Whether device is currently connected
     */
    val isConnected: Boolean,
    
    /**
     * Battery level if available (0-100)
     */
    val batteryLevel: Int? = null,
    
    /**
     * Signal strength (RSSI)
     */
    val signalStrength: Int? = null,
    
    /**
     * Supported features/capabilities
     */
    val capabilities: String? = null,
    
    /**
     * Device firmware version
     */
    val firmwareVersion: String? = null
) : Parcelable
