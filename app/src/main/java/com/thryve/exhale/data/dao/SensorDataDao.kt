package com.thryve.exhale.data.dao

import androidx.room.*
import com.thryve.exhale.data.entity.SensorData
import com.thryve.exhale.data.entity.SensorType
import com.thryve.exhale.data.entity.ConnectedDevice
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for sensor data
 */
@Dao
interface SensorDataDao {
    
    @Query("SELECT * FROM sensor_data ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentSensorData(limit: Int): Flow<List<SensorData>>
    
    @Query("SELECT * FROM sensor_data WHERE sensorType = :sensorType ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentSensorDataByType(sensorType: SensorType, limit: Int): Flow<List<SensorData>>
    
    @Query("SELECT * FROM sensor_data WHERE timestamp BETWEEN :startDate AND :endDate ORDER BY timestamp ASC")
    fun getSensorDataBetween(startDate: Date, endDate: Date): Flow<List<SensorData>>
    
    @Query("SELECT * FROM sensor_data WHERE smokingEventId = :smokingEventId ORDER BY timestamp ASC")
    fun getSensorDataForSmokingEvent(smokingEventId: Long): Flow<List<SensorData>>
    
    @Query("SELECT * FROM sensor_data WHERE isPartOfSmokingEvent = 1 ORDER BY timestamp DESC")
    fun getSensorDataFromSmokingEvents(): Flow<List<SensorData>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSensorData(sensorData: SensorData): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSensorDataList(sensorDataList: List<SensorData>)
    
    @Update
    suspend fun updateSensorData(sensorData: SensorData)
    
    @Delete
    suspend fun deleteSensorData(sensorData: SensorData)
    
    @Query("DELETE FROM sensor_data WHERE timestamp < :cutoffDate")
    suspend fun deleteOldSensorData(cutoffDate: Date): Int
    
    @Query("DELETE FROM sensor_data")
    suspend fun deleteAllSensorData()
    
    @Query("SELECT COUNT(*) FROM sensor_data")
    suspend fun getSensorDataCount(): Int
}

/**
 * Data Access Object for connected devices
 */
@Dao
interface ConnectedDeviceDao {
    
    @Query("SELECT * FROM connected_devices ORDER BY lastSeen DESC")
    fun getAllDevices(): Flow<List<ConnectedDevice>>
    
    @Query("SELECT * FROM connected_devices WHERE isConnected = 1 ORDER BY lastSeen DESC")
    fun getConnectedDevices(): Flow<List<ConnectedDevice>>
    
    @Query("SELECT * FROM connected_devices WHERE deviceId = :deviceId")
    suspend fun getDeviceById(deviceId: String): ConnectedDevice?
    
    @Query("SELECT * FROM connected_devices WHERE macAddress = :macAddress")
    suspend fun getDeviceByMacAddress(macAddress: String): ConnectedDevice?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDevice(device: ConnectedDevice)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDevices(devices: List<ConnectedDevice>)
    
    @Update
    suspend fun updateDevice(device: ConnectedDevice)
    
    @Delete
    suspend fun deleteDevice(device: ConnectedDevice)
    
    @Query("UPDATE connected_devices SET isConnected = 0")
    suspend fun markAllDevicesDisconnected()
    
    @Query("UPDATE connected_devices SET isConnected = 1, lastSeen = :timestamp WHERE deviceId = :deviceId")
    suspend fun markDeviceConnected(deviceId: String, timestamp: Date)
    
    @Query("UPDATE connected_devices SET isConnected = 0, lastSeen = :timestamp WHERE deviceId = :deviceId")
    suspend fun markDeviceDisconnected(deviceId: String, timestamp: Date)
    
    @Query("UPDATE connected_devices SET batteryLevel = :batteryLevel WHERE deviceId = :deviceId")
    suspend fun updateDeviceBatteryLevel(deviceId: String, batteryLevel: Int)
    
    @Query("UPDATE connected_devices SET signalStrength = :signalStrength WHERE deviceId = :deviceId")
    suspend fun updateDeviceSignalStrength(deviceId: String, signalStrength: Int)
    
    @Query("DELETE FROM connected_devices")
    suspend fun deleteAllDevices()
    
    @Query("SELECT COUNT(*) FROM connected_devices WHERE isConnected = 1")
    suspend fun getConnectedDeviceCount(): Int
}
