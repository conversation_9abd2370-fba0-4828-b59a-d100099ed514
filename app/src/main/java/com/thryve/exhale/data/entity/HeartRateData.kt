package com.thryve.exhale.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Entity representing heart rate measurements from wearable devices
 */
@Entity(tableName = "heart_rate_data")
@Parcelize
data class HeartRateData(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * Heart rate in beats per minute (BPM)
     */
    val heartRate: Int,
    
    /**
     * Timestamp when the measurement was taken
     */
    val timestamp: Date,
    
    /**
     * Device ID or name that provided the measurement
     */
    val deviceId: String,
    
    /**
     * Device type (e.g., "Apple Watch", "Fitbit", "Samsung Galaxy Watch")
     */
    val deviceType: String,
    
    /**
     * Confidence level of the measurement (0.0 to 1.0)
     */
    val confidence: Float = 1.0f,
    
    /**
     * Additional metadata as JSON string
     */
    val metadata: String? = null,
    
    /**
     * Whether this measurement was taken during a smoking event
     */
    val isDuringSmokingEvent: Boolean = false,
    
    /**
     * ID of associated smoking event if applicable
     */
    val smokingEventId: Long? = null
) : Parcelable {
    
    /**
     * Categorizes heart rate into zones
     */
    fun getHeartRateZone(): HeartRateZone {
        return when {
            heartRate < 60 -> HeartRateZone.RESTING
            heartRate < 100 -> HeartRateZone.NORMAL
            heartRate < 120 -> HeartRateZone.ELEVATED
            heartRate < 150 -> HeartRateZone.HIGH
            else -> HeartRateZone.VERY_HIGH
        }
    }
    
    /**
     * Checks if heart rate is considered abnormal
     */
    fun isAbnormal(): Boolean {
        return heartRate < 50 || heartRate > 120
    }
}

/**
 * Heart rate zones for categorization
 */
enum class HeartRateZone {
    RESTING,    // < 60 BPM
    NORMAL,     // 60-99 BPM
    ELEVATED,   // 100-119 BPM
    HIGH,       // 120-149 BPM
    VERY_HIGH   // >= 150 BPM
}
