package com.thryve.exhale.data.repository

import com.thryve.exhale.data.dao.SensorDataDao
import com.thryve.exhale.data.dao.ConnectedDeviceDao
import com.thryve.exhale.data.entity.SensorData
import com.thryve.exhale.data.entity.SensorType
import com.thryve.exhale.data.entity.ConnectedDevice
import kotlinx.coroutines.flow.Flow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for sensor data operations
 */
@Singleton
class SensorDataRepository @Inject constructor(
    private val sensorDataDao: SensorDataDao,
    private val connectedDeviceDao: ConnectedDeviceDao
) {
    
    // Sensor Data operations
    fun getRecentSensorData(limit: Int = 1000): Flow<List<SensorData>> = 
        sensorDataDao.getRecentSensorData(limit)
    
    fun getRecentSensorDataByType(sensorType: SensorType, limit: Int = 1000): Flow<List<SensorData>> = 
        sensorDataDao.getRecentSensorDataByType(sensorType, limit)
    
    fun getSensorDataBetween(startDate: Date, endDate: Date): Flow<List<SensorData>> = 
        sensorDataDao.getSensorDataBetween(startDate, endDate)
    
    fun getSensorDataForSmokingEvent(smokingEventId: Long): Flow<List<SensorData>> = 
        sensorDataDao.getSensorDataForSmokingEvent(smokingEventId)
    
    fun getSensorDataFromSmokingEvents(): Flow<List<SensorData>> = 
        sensorDataDao.getSensorDataFromSmokingEvents()
    
    suspend fun insertSensorData(sensorData: SensorData): Long = 
        sensorDataDao.insertSensorData(sensorData)
    
    suspend fun insertSensorDataList(sensorDataList: List<SensorData>) = 
        sensorDataDao.insertSensorDataList(sensorDataList)
    
    suspend fun updateSensorData(sensorData: SensorData) = 
        sensorDataDao.updateSensorData(sensorData)
    
    suspend fun deleteSensorData(sensorData: SensorData) = 
        sensorDataDao.deleteSensorData(sensorData)
    
    suspend fun deleteOldSensorData(cutoffDate: Date): Int = 
        sensorDataDao.deleteOldSensorData(cutoffDate)
    
    suspend fun deleteAllSensorData() = sensorDataDao.deleteAllSensorData()
    
    suspend fun getSensorDataCount(): Int = sensorDataDao.getSensorDataCount()
    
    // Connected Device operations
    fun getAllDevices(): Flow<List<ConnectedDevice>> = connectedDeviceDao.getAllDevices()
    
    fun getConnectedDevices(): Flow<List<ConnectedDevice>> = connectedDeviceDao.getConnectedDevices()
    
    suspend fun getDeviceById(deviceId: String): ConnectedDevice? = 
        connectedDeviceDao.getDeviceById(deviceId)
    
    suspend fun getDeviceByMacAddress(macAddress: String): ConnectedDevice? = 
        connectedDeviceDao.getDeviceByMacAddress(macAddress)
    
    suspend fun insertDevice(device: ConnectedDevice) = connectedDeviceDao.insertDevice(device)
    
    suspend fun insertDevices(devices: List<ConnectedDevice>) = connectedDeviceDao.insertDevices(devices)
    
    suspend fun updateDevice(device: ConnectedDevice) = connectedDeviceDao.updateDevice(device)
    
    suspend fun deleteDevice(device: ConnectedDevice) = connectedDeviceDao.deleteDevice(device)
    
    suspend fun markAllDevicesDisconnected() = connectedDeviceDao.markAllDevicesDisconnected()
    
    suspend fun markDeviceConnected(deviceId: String, timestamp: Date = Date()) = 
        connectedDeviceDao.markDeviceConnected(deviceId, timestamp)
    
    suspend fun markDeviceDisconnected(deviceId: String, timestamp: Date = Date()) = 
        connectedDeviceDao.markDeviceDisconnected(deviceId, timestamp)
    
    suspend fun updateDeviceBatteryLevel(deviceId: String, batteryLevel: Int) = 
        connectedDeviceDao.updateDeviceBatteryLevel(deviceId, batteryLevel)
    
    suspend fun updateDeviceSignalStrength(deviceId: String, signalStrength: Int) = 
        connectedDeviceDao.updateDeviceSignalStrength(deviceId, signalStrength)
    
    suspend fun deleteAllDevices() = connectedDeviceDao.deleteAllDevices()
    
    suspend fun getConnectedDeviceCount(): Int = connectedDeviceDao.getConnectedDeviceCount()
}
