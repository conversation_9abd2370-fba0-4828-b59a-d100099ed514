package com.thryve.exhale.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Entity representing detected smoking events
 */
@Entity(tableName = "smoking_events")
@Parcelize
data class SmokingEvent(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * Timestamp when smoking was detected
     */
    val startTime: Date,
    
    /**
     * Timestamp when smoking event ended (if detected)
     */
    val endTime: Date? = null,
    
    /**
     * Duration of smoking event in milliseconds
     */
    val duration: Long? = null,
    
    /**
     * Confidence level of detection (0.0 to 1.0)
     */
    val confidence: Float,
    
    /**
     * Detection method used
     */
    val detectionMethod: DetectionMethod,
    
    /**
     * Location where smoking was detected (if available)
     */
    val location: String? = null,
    
    /**
     * Latitude coordinate
     */
    val latitude: Double? = null,
    
    /**
     * Longitude coordinate
     */
    val longitude: Double? = null,
    
    /**
     * Number of puffs detected during this event
     */
    val puffCount: Int = 0,
    
    /**
     * Average heart rate during smoking event
     */
    val averageHeartRate: Int? = null,
    
    /**
     * Peak heart rate during smoking event
     */
    val peakHeartRate: Int? = null,
    
    /**
     * Heart rate before smoking event
     */
    val baselineHeartRate: Int? = null,
    
    /**
     * Additional notes or metadata
     */
    val notes: String? = null,
    
    /**
     * Whether this event was manually confirmed by user
     */
    val isConfirmed: Boolean = false,
    
    /**
     * Whether this was a false positive
     */
    val isFalsePositive: Boolean = false
) : Parcelable {
    
    /**
     * Calculates the actual duration if end time is available
     */
    fun getActualDuration(): Long? {
        return endTime?.let { end ->
            end.time - startTime.time
        }
    }
    
    /**
     * Checks if this is an ongoing smoking event
     */
    fun isOngoing(): Boolean {
        return endTime == null
    }
    
    /**
     * Gets heart rate impact (difference from baseline)
     */
    fun getHeartRateImpact(): Int? {
        return if (averageHeartRate != null && baselineHeartRate != null) {
            averageHeartRate - baselineHeartRate
        } else null
    }
}

/**
 * Methods used to detect smoking events
 */
enum class DetectionMethod {
    ACCELEROMETER,      // Hand-to-mouth gesture detection
    GYROSCOPE,          // Rotation patterns
    COMBINED_SENSORS,   // Multiple sensor fusion
    MANUAL,             // User manually logged
    HEART_RATE_PATTERN, // Heart rate pattern analysis
    MACHINE_LEARNING    // ML model prediction
}
