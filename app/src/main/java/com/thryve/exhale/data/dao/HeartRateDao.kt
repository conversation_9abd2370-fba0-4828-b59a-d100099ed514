package com.thryve.exhale.data.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.thryve.exhale.data.entity.HeartRateData
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for heart rate data
 */
@Dao
interface HeartRateDao {
    
    @Query("SELECT * FROM heart_rate_data ORDER BY timestamp DESC")
    fun getAllHeartRateData(): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentHeartRateData(limit: Int): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data WHERE timestamp BETWEEN :startDate AND :endDate ORDER BY timestamp DESC")
    fun getHeartRateDataBetween(startDate: Date, endDate: Date): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data WHERE deviceId = :deviceId ORDER BY timestamp DESC")
    fun getHeartRateDataByDevice(deviceId: String): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data WHERE isDuringSmokingEvent = 1 ORDER BY timestamp DESC")
    fun getHeartRateDataDuringSmokingEvents(): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data WHERE smokingEventId = :smokingEventId ORDER BY timestamp ASC")
    fun getHeartRateDataForSmokingEvent(smokingEventId: Long): Flow<List<HeartRateData>>
    
    @Query("SELECT AVG(heartRate) FROM heart_rate_data WHERE timestamp BETWEEN :startDate AND :endDate")
    suspend fun getAverageHeartRate(startDate: Date, endDate: Date): Double?
    
    @Query("SELECT MAX(heartRate) FROM heart_rate_data WHERE timestamp BETWEEN :startDate AND :endDate")
    suspend fun getMaxHeartRate(startDate: Date, endDate: Date): Int?
    
    @Query("SELECT MIN(heartRate) FROM heart_rate_data WHERE timestamp BETWEEN :startDate AND :endDate")
    suspend fun getMinHeartRate(startDate: Date, endDate: Date): Int?
    
    @Query("SELECT * FROM heart_rate_data WHERE heartRate > :threshold ORDER BY timestamp DESC")
    fun getHeartRateAboveThreshold(threshold: Int): Flow<List<HeartRateData>>
    
    @Query("SELECT * FROM heart_rate_data ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestHeartRateData(): HeartRateData?
    
    @Query("SELECT * FROM heart_rate_data ORDER BY timestamp DESC LIMIT 1")
    fun getLatestHeartRateDataLive(): LiveData<HeartRateData?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHeartRateData(heartRateData: HeartRateData): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHeartRateDataList(heartRateDataList: List<HeartRateData>)
    
    @Update
    suspend fun updateHeartRateData(heartRateData: HeartRateData)
    
    @Delete
    suspend fun deleteHeartRateData(heartRateData: HeartRateData)
    
    @Query("DELETE FROM heart_rate_data WHERE timestamp < :cutoffDate")
    suspend fun deleteOldHeartRateData(cutoffDate: Date): Int
    
    @Query("DELETE FROM heart_rate_data")
    suspend fun deleteAllHeartRateData()
    
    @Query("SELECT COUNT(*) FROM heart_rate_data")
    suspend fun getHeartRateDataCount(): Int
    
    @Query("SELECT COUNT(*) FROM heart_rate_data WHERE timestamp BETWEEN :startDate AND :endDate")
    suspend fun getHeartRateDataCountBetween(startDate: Date, endDate: Date): Int

    @Query("SELECT * FROM heart_rate_data ORDER BY timestamp DESC")
    suspend fun getAllHeartRateDataSync(): List<HeartRateData>
}
