package com.thryve.exhale.data.repository

import androidx.lifecycle.LiveData
import com.thryve.exhale.data.dao.HeartRateDao
import com.thryve.exhale.data.entity.HeartRateData
import kotlinx.coroutines.flow.Flow
import java.util.Date
/**
 * Repository for heart rate data operations
 */
class HeartRateRepository(
    private val heartRateDao: HeartRateDao
) {
    
    fun getAllHeartRateData(): Flow<List<HeartRateData>> = heartRateDao.getAllHeartRateData()
    
    fun getRecentHeartRateData(limit: Int = 100): Flow<List<HeartRateData>> = 
        heartRateDao.getRecentHeartRateData(limit)
    
    fun getHeartRateDataBetween(startDate: Date, endDate: Date): Flow<List<HeartRateData>> = 
        heartRateDao.getHeartRateDataBetween(startDate, endDate)
    
    fun getHeartRateDataByDevice(deviceId: String): Flow<List<HeartRateData>> = 
        heartRateDao.getHeartRateDataByDevice(deviceId)
    
    fun getHeartRateDataDuringSmokingEvents(): Flow<List<HeartRateData>> = 
        heartRateDao.getHeartRateDataDuringSmokingEvents()
    
    fun getHeartRateDataForSmokingEvent(smokingEventId: Long): Flow<List<HeartRateData>> = 
        heartRateDao.getHeartRateDataForSmokingEvent(smokingEventId)
    
    fun getHeartRateAboveThreshold(threshold: Int): Flow<List<HeartRateData>> = 
        heartRateDao.getHeartRateAboveThreshold(threshold)
    
    suspend fun getLatestHeartRateData(): HeartRateData? = heartRateDao.getLatestHeartRateData()
    
    fun getLatestHeartRateDataLive(): LiveData<HeartRateData?> = heartRateDao.getLatestHeartRateDataLive()
    
    suspend fun getAverageHeartRate(startDate: Date, endDate: Date): Double? = 
        heartRateDao.getAverageHeartRate(startDate, endDate)
    
    suspend fun getMaxHeartRate(startDate: Date, endDate: Date): Int? = 
        heartRateDao.getMaxHeartRate(startDate, endDate)
    
    suspend fun getMinHeartRate(startDate: Date, endDate: Date): Int? = 
        heartRateDao.getMinHeartRate(startDate, endDate)
    
    suspend fun insertHeartRateData(heartRateData: HeartRateData): Long = 
        heartRateDao.insertHeartRateData(heartRateData)
    
    suspend fun insertHeartRateDataList(heartRateDataList: List<HeartRateData>) = 
        heartRateDao.insertHeartRateDataList(heartRateDataList)
    
    suspend fun updateHeartRateData(heartRateData: HeartRateData) = 
        heartRateDao.updateHeartRateData(heartRateData)
    
    suspend fun deleteHeartRateData(heartRateData: HeartRateData) = 
        heartRateDao.deleteHeartRateData(heartRateData)
    
    suspend fun deleteOldHeartRateData(cutoffDate: Date): Int = 
        heartRateDao.deleteOldHeartRateData(cutoffDate)
    
    suspend fun deleteAllHeartRateData() = heartRateDao.deleteAllHeartRateData()
    
    suspend fun getHeartRateDataCount(): Int = heartRateDao.getHeartRateDataCount()
    
    suspend fun getHeartRateDataCountBetween(startDate: Date, endDate: Date): Int = 
        heartRateDao.getHeartRateDataCountBetween(startDate, endDate)
    
    /**
     * Analyzes heart rate trends and patterns
     */
    suspend fun analyzeHeartRateTrends(startDate: Date, endDate: Date): HeartRateTrends {
        val average = getAverageHeartRate(startDate, endDate) ?: 0.0
        val max = getMaxHeartRate(startDate, endDate) ?: 0
        val min = getMinHeartRate(startDate, endDate) ?: 0
        val count = getHeartRateDataCountBetween(startDate, endDate)
        
        return HeartRateTrends(
            averageHeartRate = average,
            maxHeartRate = max,
            minHeartRate = min,
            dataPointCount = count,
            startDate = startDate,
            endDate = endDate
        )
    }
}

/**
 * Data class for heart rate trend analysis
 */
data class HeartRateTrends(
    val averageHeartRate: Double,
    val maxHeartRate: Int,
    val minHeartRate: Int,
    val dataPointCount: Int,
    val startDate: Date,
    val endDate: Date
)
