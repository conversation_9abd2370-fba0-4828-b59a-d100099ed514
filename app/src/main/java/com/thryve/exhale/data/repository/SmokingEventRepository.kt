package com.thryve.exhale.data.repository

import androidx.lifecycle.LiveData
import com.thryve.exhale.data.dao.SmokingEventDao
import com.thryve.exhale.data.dao.DailySmokingCount
import com.thryve.exhale.data.dao.DailyHeartRateAverage
import com.thryve.exhale.data.entity.SmokingEvent
import com.thryve.exhale.data.entity.DetectionMethod
import kotlinx.coroutines.flow.Flow
import java.util.Date
import java.util.Calendar
/**
 * Repository for smoking event data operations
 */
class SmokingEventRepository(
    private val smokingEventDao: SmokingEventDao
) {
    
    fun getAllSmokingEvents(): Flow<List<SmokingEvent>> = smokingEventDao.getAllSmokingEvents()
    
    fun getRecentSmokingEvents(limit: Int = 50): Flow<List<SmokingEvent>> = 
        smokingEventDao.getRecentSmokingEvents(limit)
    
    fun getSmokingEventsBetween(startDate: Date, endDate: Date): Flow<List<SmokingEvent>> = 
        smokingEventDao.getSmokingEventsBetween(startDate, endDate)
    
    fun getSmokingEventsForDate(date: Date): Flow<List<SmokingEvent>> = 
        smokingEventDao.getSmokingEventsForDate(date)
    
    fun getOngoingSmokingEvents(): Flow<List<SmokingEvent>> = smokingEventDao.getOngoingSmokingEvents()
    
    fun getConfirmedSmokingEvents(): Flow<List<SmokingEvent>> = smokingEventDao.getConfirmedSmokingEvents()
    
    fun getValidSmokingEvents(): Flow<List<SmokingEvent>> = smokingEventDao.getValidSmokingEvents()
    
    fun getSmokingEventsByMethod(method: DetectionMethod): Flow<List<SmokingEvent>> = 
        smokingEventDao.getSmokingEventsByMethod(method)
    
    fun getSmokingEventsAboveConfidence(minConfidence: Float): Flow<List<SmokingEvent>> = 
        smokingEventDao.getSmokingEventsAboveConfidence(minConfidence)
    
    suspend fun getLatestSmokingEvent(): SmokingEvent? = smokingEventDao.getLatestSmokingEvent()
    
    fun getLatestSmokingEventLive(): LiveData<SmokingEvent?> = smokingEventDao.getLatestSmokingEventLive()
    
    suspend fun getSmokingEventById(id: Long): SmokingEvent? = smokingEventDao.getSmokingEventById(id)
    
    suspend fun getSmokingEventCountBetween(startDate: Date, endDate: Date): Int = 
        smokingEventDao.getSmokingEventCountBetween(startDate, endDate)
    
    suspend fun getSmokingEventCountForDate(date: Date): Int = 
        smokingEventDao.getSmokingEventCountForDate(date)
    
    suspend fun getAverageSmokingDuration(): Double? = smokingEventDao.getAverageSmokingDuration()
    
    suspend fun getAveragePuffCount(): Double? = smokingEventDao.getAveragePuffCount()
    
    suspend fun getAverageHeartRateDuringSmoking(): Double? = 
        smokingEventDao.getAverageHeartRateDuringSmoking()
    
    suspend fun insertSmokingEvent(smokingEvent: SmokingEvent): Long = 
        smokingEventDao.insertSmokingEvent(smokingEvent)
    
    suspend fun insertSmokingEvents(smokingEvents: List<SmokingEvent>) = 
        smokingEventDao.insertSmokingEvents(smokingEvents)
    
    suspend fun updateSmokingEvent(smokingEvent: SmokingEvent) = 
        smokingEventDao.updateSmokingEvent(smokingEvent)
    
    suspend fun deleteSmokingEvent(smokingEvent: SmokingEvent) = 
        smokingEventDao.deleteSmokingEvent(smokingEvent)
    
    suspend fun deleteOldSmokingEvents(cutoffDate: Date): Int = 
        smokingEventDao.deleteOldSmokingEvents(cutoffDate)
    
    suspend fun deleteAllSmokingEvents() = smokingEventDao.deleteAllSmokingEvents()
    
    suspend fun getSmokingEventCount(): Int = smokingEventDao.getSmokingEventCount()
    
    suspend fun getDailySmokingCounts(days: Int = 30): List<DailySmokingCount> = 
        smokingEventDao.getDailySmokingCounts(days)
    
    suspend fun getDailyAverageHeartRates(days: Int = 30): List<DailyHeartRateAverage> =
        smokingEventDao.getDailyAverageHeartRates(days)

    /**
     * Get today's smoking count as Flow
     */
    fun getTodaySmokingCount(): Flow<Int> {
        return smokingEventDao.getTodaySmokingCount()
    }

    /**
     * Get this week's smoking count as Flow
     */
    fun getWeekSmokingCount(): Flow<Int> {
        return smokingEventDao.getWeekSmokingCount()
    }

    /**
     * Get the last smoking event as Flow
     */
    fun getLastSmokingEvent(): Flow<SmokingEvent?> {
        return smokingEventDao.getLastSmokingEvent()
    }

    /**
     * Gets smoking statistics for today
     */
    suspend fun getTodaySmokingStats(): SmokingStats {
        val today = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time
        
        val tomorrow = Calendar.getInstance().apply {
            time = today
            add(Calendar.DAY_OF_MONTH, 1)
        }.time
        
        val count = getSmokingEventCountBetween(today, tomorrow)
        return SmokingStats(
            smokingEventCount = count,
            date = today
        )
    }
    
    /**
     * Gets smoking statistics for the past week
     */
    suspend fun getWeeklySmokingStats(): List<SmokingStats> {
        val stats = mutableListOf<SmokingStats>()
        val calendar = Calendar.getInstance()
        
        repeat(7) { dayOffset ->
            calendar.apply {
                time = Date()
                add(Calendar.DAY_OF_MONTH, -dayOffset)
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
            }
            val startOfDay = calendar.time
            
            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val endOfDay = calendar.time
            
            val count = getSmokingEventCountBetween(startOfDay, endOfDay)
            stats.add(SmokingStats(count, startOfDay))
        }
        
        return stats.reversed()
    }
}

/**
 * Data class for smoking statistics
 */
data class SmokingStats(
    val smokingEventCount: Int,
    val date: Date
)
