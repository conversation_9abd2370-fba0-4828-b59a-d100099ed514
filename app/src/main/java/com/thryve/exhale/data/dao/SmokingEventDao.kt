package com.thryve.exhale.data.dao

import androidx.room.*
import androidx.lifecycle.LiveData
import com.thryve.exhale.data.entity.SmokingEvent
import com.thryve.exhale.data.entity.DetectionMethod
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for smoking events
 */
@Dao
interface SmokingEventDao {
    
    @Query("SELECT * FROM smoking_events ORDER BY startTime DESC")
    fun getAllSmokingEvents(): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events ORDER BY startTime DESC LIMIT :limit")
    fun getRecentSmokingEvents(limit: Int): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE startTime BETWEEN :startDate AND :endDate ORDER BY startTime DESC")
    fun getSmokingEventsBetween(startDate: Date, endDate: Date): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE DATE(startTime/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') ORDER BY startTime DESC")
    fun getSmokingEventsForDate(date: Date): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE endTime IS NULL ORDER BY startTime DESC")
    fun getOngoingSmokingEvents(): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE isConfirmed = 1 ORDER BY startTime DESC")
    fun getConfirmedSmokingEvents(): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE isFalsePositive = 0 ORDER BY startTime DESC")
    fun getValidSmokingEvents(): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE detectionMethod = :method ORDER BY startTime DESC")
    fun getSmokingEventsByMethod(method: DetectionMethod): Flow<List<SmokingEvent>>
    
    @Query("SELECT * FROM smoking_events WHERE confidence >= :minConfidence ORDER BY startTime DESC")
    fun getSmokingEventsAboveConfidence(minConfidence: Float): Flow<List<SmokingEvent>>
    
    @Query("SELECT COUNT(*) FROM smoking_events WHERE startTime BETWEEN :startDate AND :endDate AND isFalsePositive = 0")
    suspend fun getSmokingEventCountBetween(startDate: Date, endDate: Date): Int
    
    @Query("SELECT COUNT(*) FROM smoking_events WHERE DATE(startTime/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') AND isFalsePositive = 0")
    suspend fun getSmokingEventCountForDate(date: Date): Int
    
    @Query("SELECT AVG(duration) FROM smoking_events WHERE duration IS NOT NULL AND isFalsePositive = 0")
    suspend fun getAverageSmokingDuration(): Double?
    
    @Query("SELECT AVG(puffCount) FROM smoking_events WHERE puffCount > 0 AND isFalsePositive = 0")
    suspend fun getAveragePuffCount(): Double?
    
    @Query("SELECT AVG(averageHeartRate) FROM smoking_events WHERE averageHeartRate IS NOT NULL AND isFalsePositive = 0")
    suspend fun getAverageHeartRateDuringSmoking(): Double?
    
    @Query("SELECT * FROM smoking_events ORDER BY startTime DESC LIMIT 1")
    suspend fun getLatestSmokingEvent(): SmokingEvent?
    
    @Query("SELECT * FROM smoking_events ORDER BY startTime DESC LIMIT 1")
    fun getLatestSmokingEventLive(): LiveData<SmokingEvent?>
    
    @Query("SELECT * FROM smoking_events WHERE id = :id")
    suspend fun getSmokingEventById(id: Long): SmokingEvent?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSmokingEvent(smokingEvent: SmokingEvent): Long
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSmokingEvents(smokingEvents: List<SmokingEvent>)
    
    @Update
    suspend fun updateSmokingEvent(smokingEvent: SmokingEvent)
    
    @Delete
    suspend fun deleteSmokingEvent(smokingEvent: SmokingEvent)
    
    @Query("DELETE FROM smoking_events WHERE startTime < :cutoffDate")
    suspend fun deleteOldSmokingEvents(cutoffDate: Date): Int
    
    @Query("DELETE FROM smoking_events")
    suspend fun deleteAllSmokingEvents()
    
    @Query("SELECT COUNT(*) FROM smoking_events")
    suspend fun getSmokingEventCount(): Int
    
    // Statistics queries
    @Query("""
        SELECT COUNT(*) as count, DATE(startTime/1000, 'unixepoch') as date 
        FROM smoking_events 
        WHERE isFalsePositive = 0 
        GROUP BY DATE(startTime/1000, 'unixepoch') 
        ORDER BY date DESC 
        LIMIT :days
    """)
    suspend fun getDailySmokingCounts(days: Int): List<DailySmokingCount>
    
    @Query("""
        SELECT AVG(averageHeartRate) as avgHeartRate, DATE(startTime/1000, 'unixepoch') as date 
        FROM smoking_events 
        WHERE averageHeartRate IS NOT NULL AND isFalsePositive = 0 
        GROUP BY DATE(startTime/1000, 'unixepoch') 
        ORDER BY date DESC 
        LIMIT :days
    """)
    suspend fun getDailyAverageHeartRates(days: Int): List<DailyHeartRateAverage>
}

/**
 * Data class for daily smoking count statistics
 */
data class DailySmokingCount(
    val count: Int,
    val date: String
)

/**
 * Data class for daily heart rate averages
 */
data class DailyHeartRateAverage(
    val avgHeartRate: Double,
    val date: String
)
