package com.thryve.exhale.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.thryve.exhale.data.converter.DateConverter
import com.thryve.exhale.data.dao.*
import com.thryve.exhale.data.entity.*

/**
 * Room database for the Exhale application
 */
@Database(
    entities = [
        HeartRateData::class,
        SmokingEvent::class,
        SensorData::class,
        ConnectedDevice::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(DateConverter::class)
abstract class ExhaleDatabase : RoomDatabase() {
    
    abstract fun heartRateDao(): HeartRateDao
    abstract fun smokingEventDao(): SmokingEventDao
    abstract fun sensorDataDao(): SensorDataDao
    abstract fun connectedDeviceDao(): ConnectedDeviceDao
    
    companion object {
        @Volatile
        private var INSTANCE: ExhaleDatabase? = null
        
        fun getDatabase(context: Context): ExhaleDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ExhaleDatabase::class.java,
                    "exhale_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
