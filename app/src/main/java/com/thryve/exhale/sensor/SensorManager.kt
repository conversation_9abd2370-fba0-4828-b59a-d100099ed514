package com.thryve.exhale.sensor

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager as AndroidSensorManager
import android.util.Log
import com.thryve.exhale.data.entity.SensorData
import com.thryve.exhale.data.entity.SensorType
import com.thryve.exhale.data.repository.SensorDataRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages sensor data collection for smoking detection
 */
@Singleton
class SensorManager @Inject constructor(
    private val context: Context,
    private val sensorDataRepository: SensorDataRepository
) : SensorEventListener {
    
    private val androidSensorManager = context.getSystemService(Context.SENSOR_SERVICE) as AndroidSensorManager
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Sensor data flows
    private val _accelerometerData = MutableSharedFlow<SensorData>()
    val accelerometerData: SharedFlow<SensorData> = _accelerometerData.asSharedFlow()
    
    private val _gyroscopeData = MutableSharedFlow<SensorData>()
    val gyroscopeData: SharedFlow<SensorData> = _gyroscopeData.asSharedFlow()
    
    private val _magnetometerData = MutableSharedFlow<SensorData>()
    val magnetometerData: SharedFlow<SensorData> = _magnetometerData.asSharedFlow()
    
    private val _linearAccelerationData = MutableSharedFlow<SensorData>()
    val linearAccelerationData: SharedFlow<SensorData> = _linearAccelerationData.asSharedFlow()
    
    // Sensors
    private val accelerometer = androidSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    private val gyroscope = androidSensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE)
    private val magnetometer = androidSensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)
    private val linearAcceleration = androidSensorManager.getDefaultSensor(Sensor.TYPE_LINEAR_ACCELERATION)
    private val gravity = androidSensorManager.getDefaultSensor(Sensor.TYPE_GRAVITY)
    private val rotationVector = androidSensorManager.getDefaultSensor(Sensor.TYPE_ROTATION_VECTOR)
    
    // State
    private var isCollecting = false
    private var samplingRate = AndroidSensorManager.SENSOR_DELAY_NORMAL
    
    companion object {
        private const val TAG = "SensorManager"
    }
    
    /**
     * Starts sensor data collection
     */
    fun startCollection(samplingRate: Int = AndroidSensorManager.SENSOR_DELAY_NORMAL) {
        if (isCollecting) {
            Log.w(TAG, "Sensor collection already started")
            return
        }
        
        this.samplingRate = samplingRate
        
        // Register sensor listeners
        accelerometer?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Accelerometer registered")
        }
        
        gyroscope?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Gyroscope registered")
        }
        
        magnetometer?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Magnetometer registered")
        }
        
        linearAcceleration?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Linear acceleration registered")
        }
        
        gravity?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Gravity sensor registered")
        }
        
        rotationVector?.let { sensor ->
            androidSensorManager.registerListener(this, sensor, samplingRate)
            Log.d(TAG, "Rotation vector registered")
        }
        
        isCollecting = true
        Log.i(TAG, "Sensor collection started")
    }
    
    /**
     * Stops sensor data collection
     */
    fun stopCollection() {
        if (!isCollecting) {
            Log.w(TAG, "Sensor collection not started")
            return
        }
        
        androidSensorManager.unregisterListener(this)
        isCollecting = false
        Log.i(TAG, "Sensor collection stopped")
    }
    
    /**
     * Checks if sensors are available
     */
    fun checkSensorAvailability(): SensorAvailability {
        return SensorAvailability(
            hasAccelerometer = accelerometer != null,
            hasGyroscope = gyroscope != null,
            hasMagnetometer = magnetometer != null,
            hasLinearAcceleration = linearAcceleration != null,
            hasGravity = gravity != null,
            hasRotationVector = rotationVector != null
        )
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        event?.let { sensorEvent ->
            val timestamp = Date()
            val sensorData = when (sensorEvent.sensor.type) {
                Sensor.TYPE_ACCELEROMETER -> {
                    SensorData(
                        sensorType = SensorType.ACCELEROMETER,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                Sensor.TYPE_GYROSCOPE -> {
                    SensorData(
                        sensorType = SensorType.GYROSCOPE,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                Sensor.TYPE_MAGNETIC_FIELD -> {
                    SensorData(
                        sensorType = SensorType.MAGNETOMETER,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                Sensor.TYPE_LINEAR_ACCELERATION -> {
                    SensorData(
                        sensorType = SensorType.LINEAR_ACCELERATION,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                Sensor.TYPE_GRAVITY -> {
                    SensorData(
                        sensorType = SensorType.GRAVITY,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                Sensor.TYPE_ROTATION_VECTOR -> {
                    SensorData(
                        sensorType = SensorType.ROTATION_VECTOR,
                        timestamp = timestamp,
                        x = sensorEvent.values[0],
                        y = sensorEvent.values[1],
                        z = sensorEvent.values[2],
                        accuracy = sensorEvent.accuracy
                    )
                }
                else -> null
            }
            
            sensorData?.let { data ->
                // Emit to appropriate flow
                scope.launch {
                    when (data.sensorType) {
                        SensorType.ACCELEROMETER -> _accelerometerData.emit(data)
                        SensorType.GYROSCOPE -> _gyroscopeData.emit(data)
                        SensorType.MAGNETOMETER -> _magnetometerData.emit(data)
                        SensorType.LINEAR_ACCELERATION -> _linearAccelerationData.emit(data)
                        else -> { /* Handle other sensor types */ }
                    }
                    
                    // Store in database (with sampling to avoid overwhelming storage)
                    if (System.currentTimeMillis() % 10 == 0L) { // Store every 10th sample
                        sensorDataRepository.insertSensorData(data)
                    }
                }
            }
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        Log.d(TAG, "Sensor accuracy changed: ${sensor?.name} -> $accuracy")
    }
    
    /**
     * Cleans up resources
     */
    fun cleanup() {
        stopCollection()
        scope.cancel()
    }
}

/**
 * Data class for sensor availability information
 */
data class SensorAvailability(
    val hasAccelerometer: Boolean,
    val hasGyroscope: Boolean,
    val hasMagnetometer: Boolean,
    val hasLinearAcceleration: Boolean,
    val hasGravity: Boolean,
    val hasRotationVector: Boolean
) {
    val hasRequiredSensors: Boolean
        get() = hasAccelerometer // Minimum requirement
    
    val hasOptimalSensors: Boolean
        get() = hasAccelerometer && hasGyroscope
}
