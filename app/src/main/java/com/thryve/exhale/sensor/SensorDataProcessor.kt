package com.thryve.exhale.sensor

import com.thryve.exhale.data.entity.SensorData
import com.thryve.exhale.data.entity.SensorType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.scan
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * Processes and filters sensor data for smoking detection
 */
@Singleton
class SensorDataProcessor @Inject constructor() {
    
    companion object {
        private const val GRAVITY_EARTH = 9.80665f
        private const val LOW_PASS_ALPHA = 0.8f
        private const val HIGH_PASS_ALPHA = 0.1f
    }
    
    /**
     * Applies low-pass filter to remove high-frequency noise
     */
    fun applyLowPassFilter(sensorDataFlow: Flow<SensorData>): Flow<SensorData> {
        return sensorDataFlow.scan(null as SensorData?) { previous, current ->
            if (previous == null) {
                current
            } else {
                current.copy(
                    processedX = LOW_PASS_ALPHA * previous.processedX!! + (1 - LOW_PASS_ALPHA) * current.x,
                    processedY = LOW_PASS_ALPHA * previous.processedY!! + (1 - LOW_PASS_ALPHA) * current.y,
                    processedZ = LOW_PASS_ALPHA * previous.processedZ!! + (1 - LOW_PASS_ALPHA) * current.z
                )
            }
        }.map { it!! }
    }
    
    /**
     * Applies high-pass filter to remove gravity component
     */
    fun applyHighPassFilter(sensorDataFlow: Flow<SensorData>): Flow<SensorData> {
        return sensorDataFlow.scan(null as SensorData?) { previous, current ->
            if (previous == null) {
                current.copy(
                    processedX = current.x,
                    processedY = current.y,
                    processedZ = current.z
                )
            } else {
                val filteredX = HIGH_PASS_ALPHA * (previous.processedX!! + current.x - previous.x)
                val filteredY = HIGH_PASS_ALPHA * (previous.processedY!! + current.y - previous.y)
                val filteredZ = HIGH_PASS_ALPHA * (previous.processedZ!! + current.z - previous.z)
                
                current.copy(
                    processedX = filteredX,
                    processedY = filteredY,
                    processedZ = filteredZ
                )
            }
        }.map { it!! }
    }
    
    /**
     * Calculates magnitude of sensor vector
     */
    fun calculateMagnitude(sensorData: SensorData): Float {
        val x = sensorData.processedX ?: sensorData.x
        val y = sensorData.processedY ?: sensorData.y
        val z = sensorData.processedZ ?: sensorData.z
        return sqrt(x * x + y * y + z * z)
    }
    
    /**
     * Detects significant motion changes
     */
    fun detectMotionChanges(sensorDataFlow: Flow<SensorData>, threshold: Float = 2.0f): Flow<MotionEvent> {
        return sensorDataFlow.scan(null as Pair<SensorData, Float>?) { previous, current ->
            val magnitude = calculateMagnitude(current)
            Pair(current, magnitude)
        }.map { pair ->
            if (pair != null) {
                val (current, magnitude) = pair
                MotionEvent(
                    sensorData = current,
                    magnitude = magnitude,
                    isSignificant = magnitude > threshold
                )
            } else {
                null
            }
        }.map { it!! }
    }
    
    /**
     * Analyzes hand-to-mouth gesture patterns
     */
    fun analyzeHandToMouthGesture(
        accelerometerData: List<SensorData>,
        gyroscopeData: List<SensorData>
    ): GestureAnalysis {
        if (accelerometerData.isEmpty() || gyroscopeData.isEmpty()) {
            return GestureAnalysis(
                isHandToMouthGesture = false,
                confidence = 0.0f,
                duration = 0L,
                peakAcceleration = 0.0f,
                rotationPattern = emptyList()
            )
        }
        
        // Analyze acceleration patterns
        val accelerationMagnitudes = accelerometerData.map { calculateMagnitude(it) }
        val peakAcceleration = accelerationMagnitudes.maxOrNull() ?: 0.0f
        val averageAcceleration = accelerationMagnitudes.average().toFloat()
        
        // Analyze rotation patterns
        val rotationMagnitudes = gyroscopeData.map { calculateMagnitude(it) }
        val peakRotation = rotationMagnitudes.maxOrNull() ?: 0.0f
        
        // Simple heuristic for hand-to-mouth gesture detection
        val hasSignificantAcceleration = peakAcceleration > 5.0f
        val hasControlledRotation = peakRotation > 0.5f && peakRotation < 3.0f
        val hasReasonableDuration = accelerometerData.size > 10 && accelerometerData.size < 100
        
        val isGesture = hasSignificantAcceleration && hasControlledRotation && hasReasonableDuration
        val confidence = when {
            isGesture -> 0.7f + (peakAcceleration / 20.0f).coerceAtMost(0.3f)
            else -> 0.1f + (peakAcceleration / 50.0f).coerceAtMost(0.2f)
        }
        
        val duration = if (accelerometerData.isNotEmpty()) {
            accelerometerData.last().timestamp.time - accelerometerData.first().timestamp.time
        } else 0L
        
        return GestureAnalysis(
            isHandToMouthGesture = isGesture,
            confidence = confidence,
            duration = duration,
            peakAcceleration = peakAcceleration,
            rotationPattern = rotationMagnitudes
        )
    }
    
    /**
     * Detects repetitive motion patterns (like puffing)
     */
    fun detectRepetitiveMotion(
        sensorDataList: List<SensorData>,
        windowSize: Int = 20
    ): RepetitiveMotionAnalysis {
        if (sensorDataList.size < windowSize) {
            return RepetitiveMotionAnalysis(
                hasRepetitivePattern = false,
                frequency = 0.0f,
                amplitude = 0.0f,
                confidence = 0.0f
            )
        }
        
        val magnitudes = sensorDataList.map { calculateMagnitude(it) }
        
        // Simple frequency analysis using peak detection
        val peaks = detectPeaks(magnitudes)
        val timeDiffs = peaks.zipWithNext { a, b -> b - a }
        
        val hasPattern = peaks.size >= 3 && timeDiffs.isNotEmpty()
        val averageInterval = if (timeDiffs.isNotEmpty()) timeDiffs.average() else 0.0
        val frequency = if (averageInterval > 0) (1000.0 / averageInterval).toFloat() else 0.0f
        
        val amplitude = if (peaks.isNotEmpty()) {
            peaks.map { magnitudes[it] }.average().toFloat()
        } else 0.0f
        
        val confidence = when {
            hasPattern && frequency in 0.5f..3.0f -> 0.8f // Typical smoking frequency
            hasPattern -> 0.4f
            else -> 0.1f
        }
        
        return RepetitiveMotionAnalysis(
            hasRepetitivePattern = hasPattern,
            frequency = frequency,
            amplitude = amplitude,
            confidence = confidence
        )
    }
    
    /**
     * Simple peak detection algorithm
     */
    private fun detectPeaks(data: List<Float>, threshold: Float = 0.5f): List<Int> {
        val peaks = mutableListOf<Int>()
        
        for (i in 1 until data.size - 1) {
            if (data[i] > data[i - 1] && data[i] > data[i + 1] && data[i] > threshold) {
                peaks.add(i)
            }
        }
        
        return peaks
    }
}

/**
 * Represents a motion event with magnitude and significance
 */
data class MotionEvent(
    val sensorData: SensorData,
    val magnitude: Float,
    val isSignificant: Boolean
)

/**
 * Analysis result for hand-to-mouth gesture detection
 */
data class GestureAnalysis(
    val isHandToMouthGesture: Boolean,
    val confidence: Float,
    val duration: Long,
    val peakAcceleration: Float,
    val rotationPattern: List<Float>
)

/**
 * Analysis result for repetitive motion detection
 */
data class RepetitiveMotionAnalysis(
    val hasRepetitivePattern: Boolean,
    val frequency: Float, // Hz
    val amplitude: Float,
    val confidence: Float
)
