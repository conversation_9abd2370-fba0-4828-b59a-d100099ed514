package com.thryve.exhale.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.*
import android.bluetooth.le.*
import android.content.Context
import android.os.ParcelUuid
import android.util.Log
import com.thryve.exhale.data.entity.ConnectedDevice
import com.thryve.exhale.data.entity.HeartRateData
import com.thryve.exhale.data.repository.SensorDataRepository
import com.thryve.exhale.data.repository.HeartRateRepository
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.*
/**
 * Manages Bluetooth Low Energy connections to wearable devices
 */
class BleManager(
    private val context: Context,
    private val sensorDataRepository: SensorDataRepository,
    private val heartRateRepository: HeartRateRepository
) {
    
    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bluetoothAdapter = bluetoothManager.adapter
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // BLE Scanner
    private val bleScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    private var isScanning = false
    
    // Connected devices
    private val connectedDevices = mutableMapOf<String, BluetoothGatt>()
    
    // Events
    private val _deviceDiscovered = MutableSharedFlow<BluetoothDevice>()
    val deviceDiscovered: SharedFlow<BluetoothDevice> = _deviceDiscovered.asSharedFlow()
    
    private val _deviceConnected = MutableSharedFlow<ConnectedDevice>()
    val deviceConnected: SharedFlow<ConnectedDevice> = _deviceConnected.asSharedFlow()
    
    private val _deviceDisconnected = MutableSharedFlow<String>()
    val deviceDisconnected: SharedFlow<String> = _deviceDisconnected.asSharedFlow()
    
    private val _heartRateReceived = MutableSharedFlow<HeartRateData>()
    val heartRateReceived: SharedFlow<HeartRateData> = _heartRateReceived.asSharedFlow()
    
    companion object {
        private const val TAG = "BleManager"
        
        // Standard Bluetooth UUIDs
        val HEART_RATE_SERVICE_UUID: UUID = UUID.fromString("0000180D-0000-1000-8000-00805F9B34FB")
        val HEART_RATE_MEASUREMENT_UUID: UUID = UUID.fromString("00002A37-0000-1000-8000-00805F9B34FB")
        val CLIENT_CHARACTERISTIC_CONFIG_UUID: UUID = UUID.fromString("00002902-0000-1000-8000-00805F9B34FB")
        val BATTERY_SERVICE_UUID: UUID = UUID.fromString("0000180F-0000-1000-8000-00805F9B34FB")
        val BATTERY_LEVEL_UUID: UUID = UUID.fromString("00002A19-0000-1000-8000-00805F9B34FB")
        
        private const val SCAN_TIMEOUT = 30000L // 30 seconds
    }
    
    /**
     * Starts scanning for BLE devices
     */
    @SuppressLint("MissingPermission")
    fun startScanning() {
        if (!bluetoothAdapter.isEnabled) {
            Log.w(TAG, "Bluetooth is not enabled")
            return
        }
        
        if (isScanning) {
            Log.w(TAG, "Already scanning")
            return
        }
        
        val scanSettings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            .build()
        
        val scanFilters = listOf(
            ScanFilter.Builder()
                .setServiceUuid(ParcelUuid(HEART_RATE_SERVICE_UUID))
                .build()
        )
        
        bleScanner?.startScan(scanFilters, scanSettings, scanCallback)
        isScanning = true
        
        // Stop scanning after timeout
        scope.launch {
            delay(SCAN_TIMEOUT)
            stopScanning()
        }
        
        Log.i(TAG, "Started BLE scanning")
    }
    
    /**
     * Stops scanning for BLE devices
     */
    @SuppressLint("MissingPermission")
    fun stopScanning() {
        if (!isScanning) return
        
        bleScanner?.stopScan(scanCallback)
        isScanning = false
        Log.i(TAG, "Stopped BLE scanning")
    }
    
    /**
     * Connects to a BLE device
     */
    @SuppressLint("MissingPermission")
    fun connectToDevice(device: BluetoothDevice) {
        if (connectedDevices.containsKey(device.address)) {
            Log.w(TAG, "Device already connected: ${device.address}")
            return
        }
        
        Log.i(TAG, "Connecting to device: ${device.name} (${device.address})")
        device.connectGatt(context, false, gattCallback)
    }
    
    /**
     * Disconnects from a BLE device
     */
    @SuppressLint("MissingPermission")
    fun disconnectDevice(deviceAddress: String) {
        connectedDevices[deviceAddress]?.let { gatt ->
            gatt.disconnect()
            gatt.close()
            connectedDevices.remove(deviceAddress)
            
            scope.launch {
                sensorDataRepository.markDeviceDisconnected(deviceAddress)
                _deviceDisconnected.emit(deviceAddress)
            }
            
            Log.i(TAG, "Disconnected from device: $deviceAddress")
        }
    }
    
    /**
     * Disconnects all connected devices
     */
    fun disconnectAllDevices() {
        connectedDevices.keys.toList().forEach { address ->
            disconnectDevice(address)
        }
    }
    
    /**
     * Gets list of connected device addresses
     */
    fun getConnectedDeviceAddresses(): List<String> {
        return connectedDevices.keys.toList()
    }
    
    /**
     * Checks if Bluetooth is enabled and available
     */
    fun isBluetoothAvailable(): Boolean {
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled
    }
    
    // BLE Scan Callback
    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult?) {
            result?.device?.let { device ->
                scope.launch {
                    _deviceDiscovered.emit(device)
                }
                Log.d(TAG, "Discovered device: ${device.name} (${device.address})")
            }
        }
        
        override fun onScanFailed(errorCode: Int) {
            Log.e(TAG, "BLE scan failed with error code: $errorCode")
            isScanning = false
        }
    }
    
    // GATT Callback for device connections
    private val gattCallback = object : BluetoothGattCallback() {
        @SuppressLint("MissingPermission")
        override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    Log.i(TAG, "Connected to GATT server: ${gatt?.device?.address}")
                    connectedDevices[gatt?.device?.address ?: ""] = gatt!!
                    gatt.discoverServices()
                }
                BluetoothProfile.STATE_DISCONNECTED -> {
                    Log.i(TAG, "Disconnected from GATT server: ${gatt?.device?.address}")
                    gatt?.device?.address?.let { address ->
                        connectedDevices.remove(address)
                        scope.launch {
                            sensorDataRepository.markDeviceDisconnected(address)
                            _deviceDisconnected.emit(address)
                        }
                    }
                    gatt?.close()
                }
            }
        }
        
        @SuppressLint("MissingPermission")
        override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.i(TAG, "Services discovered for device: ${gatt?.device?.address}")
                
                gatt?.let { bluetoothGatt ->
                    // Save device info
                    scope.launch {
                        saveConnectedDevice(bluetoothGatt)
                    }
                    
                    // Enable heart rate notifications
                    enableHeartRateNotifications(bluetoothGatt)
                    
                    // Read battery level
                    readBatteryLevel(bluetoothGatt)
                }
            } else {
                Log.w(TAG, "Service discovery failed with status: $status")
            }
        }
        
        override fun onCharacteristicChanged(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?) {
            characteristic?.let { char ->
                when (char.uuid) {
                    HEART_RATE_MEASUREMENT_UUID -> {
                        val heartRate = parseHeartRateData(char.value)
                        scope.launch {
                            handleHeartRateData(gatt?.device, heartRate)
                        }
                    }
                }
            }
        }
        
        override fun onCharacteristicRead(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?, status: Int) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                characteristic?.let { char ->
                    when (char.uuid) {
                        BATTERY_LEVEL_UUID -> {
                            val batteryLevel = char.getIntValue(BluetoothGattCharacteristic.FORMAT_UINT8, 0) ?: 0
                            scope.launch {
                                gatt?.device?.address?.let { address ->
                                    sensorDataRepository.updateDeviceBatteryLevel(address, batteryLevel)
                                }
                            }
                            Log.d(TAG, "Battery level: $batteryLevel%")
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Saves connected device information to database
     */
    private suspend fun saveConnectedDevice(gatt: BluetoothGatt) {
        val device = gatt.device
        val connectedDevice = ConnectedDevice(
            deviceId = device.address,
            deviceName = device.name ?: "Unknown Device",
            deviceType = determineDeviceType(device),
            macAddress = device.address,
            firstConnected = Date(),
            lastSeen = Date(),
            isConnected = true
        )
        
        sensorDataRepository.insertDevice(connectedDevice)
        _deviceConnected.emit(connectedDevice)
        
        Log.i(TAG, "Saved connected device: ${connectedDevice.deviceName}")
    }
    
    /**
     * Determines device type based on device information
     */
    @SuppressLint("MissingPermission")
    private fun determineDeviceType(device: BluetoothDevice): String {
        return when {
            device.name?.contains("Apple Watch", ignoreCase = true) == true -> "Apple Watch"
            device.name?.contains("Galaxy Watch", ignoreCase = true) == true -> "Samsung Galaxy Watch"
            device.name?.contains("Fitbit", ignoreCase = true) == true -> "Fitbit"
            device.name?.contains("Garmin", ignoreCase = true) == true -> "Garmin"
            device.name?.contains("Polar", ignoreCase = true) == true -> "Polar"
            else -> "Unknown Wearable"
        }
    }
    
    /**
     * Enables heart rate notifications
     */
    @SuppressLint("MissingPermission")
    private fun enableHeartRateNotifications(gatt: BluetoothGatt) {
        val heartRateService = gatt.getService(HEART_RATE_SERVICE_UUID)
        val heartRateCharacteristic = heartRateService?.getCharacteristic(HEART_RATE_MEASUREMENT_UUID)
        
        heartRateCharacteristic?.let { characteristic ->
            gatt.setCharacteristicNotification(characteristic, true)
            
            val descriptor = characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
            descriptor?.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
            gatt.writeDescriptor(descriptor)
            
            Log.i(TAG, "Enabled heart rate notifications for ${gatt.device.address}")
        }
    }
    
    /**
     * Reads battery level from device
     */
    @SuppressLint("MissingPermission")
    private fun readBatteryLevel(gatt: BluetoothGatt) {
        val batteryService = gatt.getService(BATTERY_SERVICE_UUID)
        val batteryCharacteristic = batteryService?.getCharacteristic(BATTERY_LEVEL_UUID)
        
        batteryCharacteristic?.let { characteristic ->
            gatt.readCharacteristic(characteristic)
        }
    }
    
    /**
     * Parses heart rate data from BLE characteristic
     */
    private fun parseHeartRateData(data: ByteArray): Int {
        return if (data.isNotEmpty()) {
            // Heart rate format flag
            val format = data[0].toInt() and 0x01
            
            if (format == 0) {
                // 8-bit heart rate value
                data[1].toInt() and 0xFF
            } else {
                // 16-bit heart rate value
                ((data[2].toInt() and 0xFF) shl 8) or (data[1].toInt() and 0xFF)
            }
        } else 0
    }
    
    /**
     * Handles received heart rate data
     */
    private suspend fun handleHeartRateData(device: BluetoothDevice?, heartRate: Int) {
        if (device == null || heartRate <= 0) return
        
        val heartRateData = HeartRateData(
            heartRate = heartRate,
            timestamp = Date(),
            deviceId = device.address,
            deviceType = determineDeviceType(device),
            confidence = 1.0f
        )
        
        // Save to database
        heartRateRepository.insertHeartRateData(heartRateData)
        
        // Emit event
        _heartRateReceived.emit(heartRateData)
        
        Log.d(TAG, "Heart rate received: $heartRate BPM from ${device.address}")
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopScanning()
        disconnectAllDevices()
        scope.cancel()
    }
}
