package com.thryve.exhale.viewmodel

import android.app.Application
import android.bluetooth.BluetoothDevice
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.thryve.exhale.bluetooth.BleManager
import com.thryve.exhale.data.database.ExhaleDatabase
import com.thryve.exhale.data.repository.HeartRateRepository
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * ViewModel for device management functionality
 */
class DeviceManagementViewModel(application: Application) : AndroidViewModel(application) {
    
    private val context: Context = application.applicationContext
    private val database = ExhaleDatabase.getDatabase(context)
    private val heartRateRepository = HeartRateRepository(database.heartRateDao())
    private val bleManager = BleManager(context, heartRateRepository)
    
    private val _uiState = MutableStateFlow(DeviceManagementUiState())
    val uiState: StateFlow<DeviceManagementUiState> = _uiState.asStateFlow()
    
    private val _connectedDevices = MutableStateFlow<List<ConnectedDeviceInfo>>(emptyList())
    val connectedDevices: StateFlow<List<ConnectedDeviceInfo>> = _connectedDevices.asStateFlow()
    
    private val _availableDevices = MutableStateFlow<List<AvailableDeviceInfo>>(emptyList())
    val availableDevices: StateFlow<List<AvailableDeviceInfo>> = _availableDevices.asStateFlow()
    
    private val _messages = MutableSharedFlow<String>()
    val messages: SharedFlow<String> = _messages.asSharedFlow()
    
    init {
        observeBleManager()
    }
    
    private fun observeBleManager() {
        viewModelScope.launch {
            bleManager.connectedDevices.collect { devices ->
                _connectedDevices.value = devices.map { device ->
                    ConnectedDeviceInfo(
                        device = device,
                        connectionStatus = "Connected",
                        lastHeartRate = null // TODO: Get last heart rate from repository
                    )
                }
            }
        }
        
        viewModelScope.launch {
            bleManager.discoveredDevices.collect { devices ->
                _availableDevices.value = devices.map { device ->
                    AvailableDeviceInfo(
                        device = device,
                        signalStrength = "Strong", // TODO: Get actual RSSI
                        deviceType = determineDeviceType(device)
                    )
                }
            }
        }
    }
    
    /**
     * Loads currently connected devices
     */
    fun loadConnectedDevices() {
        // Connected devices are automatically loaded through BleManager observation
    }
    
    /**
     * Starts scanning for available devices
     */
    fun startDeviceScan() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isScanning = true)
            
            try {
                bleManager.startScanning()
                _messages.emit("Scanning for devices...")
                
                // Stop scanning after 30 seconds
                kotlinx.coroutines.delay(30000)
                bleManager.stopScanning()
                _messages.emit("Scan completed")
            } catch (e: Exception) {
                _messages.emit("Error scanning for devices: ${e.message}")
            } finally {
                _uiState.value = _uiState.value.copy(isScanning = false)
            }
        }
    }
    
    /**
     * Connects to a specific device
     */
    fun connectToDevice(device: BluetoothDevice) {
        viewModelScope.launch {
            try {
                _messages.emit("Connecting to ${device.name ?: "Unknown Device"}...")
                bleManager.connectToDevice(device)
                _messages.emit("Connected to ${device.name ?: "Unknown Device"}")
            } catch (e: Exception) {
                _messages.emit("Failed to connect to ${device.name ?: "Unknown Device"}: ${e.message}")
            }
        }
    }
    
    /**
     * Disconnects from a specific device
     */
    fun disconnectDevice(device: BluetoothDevice) {
        viewModelScope.launch {
            try {
                bleManager.disconnectDevice(device)
                _messages.emit("Disconnected from ${device.name ?: "Unknown Device"}")
            } catch (e: Exception) {
                _messages.emit("Failed to disconnect from ${device.name ?: "Unknown Device"}: ${e.message}")
            }
        }
    }
    
    private fun determineDeviceType(device: BluetoothDevice): String {
        val deviceName = device.name?.lowercase() ?: ""
        return when {
            deviceName.contains("watch") -> "Smartwatch"
            deviceName.contains("band") -> "Fitness Band"
            deviceName.contains("heart") -> "Heart Rate Monitor"
            deviceName.contains("apple") -> "Apple Device"
            deviceName.contains("samsung") -> "Samsung Device"
            deviceName.contains("fitbit") -> "Fitbit Device"
            deviceName.contains("garmin") -> "Garmin Device"
            else -> "Wearable Device"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        bleManager.cleanup()
    }
    
    /**
     * UI state for device management screen
     */
    data class DeviceManagementUiState(
        val isScanning: Boolean = false
    )
    
    /**
     * Information about a connected device
     */
    data class ConnectedDeviceInfo(
        val device: BluetoothDevice,
        val connectionStatus: String,
        val lastHeartRate: Int?
    )
    
    /**
     * Information about an available device
     */
    data class AvailableDeviceInfo(
        val device: BluetoothDevice,
        val signalStrength: String,
        val deviceType: String
    )
}
