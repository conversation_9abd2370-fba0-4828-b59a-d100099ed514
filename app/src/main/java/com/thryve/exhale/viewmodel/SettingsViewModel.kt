package com.thryve.exhale.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.thryve.exhale.bluetooth.BleManager
import com.thryve.exhale.data.database.ExhaleDatabase
import com.thryve.exhale.data.repository.HeartRateRepository
import com.thryve.exhale.data.repository.SmokingEventRepository
import com.thryve.exhale.data.repository.SensorDataRepository
import com.thryve.exhale.notification.NotificationPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

/**
 * ViewModel for settings and configuration
 */
class SettingsViewModel(application: Application) : AndroidViewModel(application) {
    
    private val context: Context = application.applicationContext
    private val database = ExhaleDatabase.getDatabase(context)
    private val smokingEventRepository = SmokingEventRepository(database.smokingEventDao())
    private val heartRateRepository = HeartRateRepository(database.heartRateDao())
    private val sensorDataRepository = SensorDataRepository(database.sensorDataDao())
    private val bleManager = BleManager(context, heartRateRepository)
    private val notificationPreferences = NotificationPreferences(context)
    
    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()
    
    private val _messages = MutableSharedFlow<String>()
    val messages: SharedFlow<String> = _messages.asSharedFlow()
    
    /**
     * Starts scanning for Bluetooth devices
     */
    fun startDeviceScan() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isScanning = true)
            
            try {
                bleManager.startScanning()
                _messages.emit("Scanning for devices...")
                
                // Stop scanning after 30 seconds
                kotlinx.coroutines.delay(30000)
                bleManager.stopScanning()
                _messages.emit("Scan completed")
            } catch (e: Exception) {
                _messages.emit("Error scanning for devices: ${e.message}")
            } finally {
                _uiState.value = _uiState.value.copy(isScanning = false)
            }
        }
    }
    
    /**
     * Updates smoking detection sensitivity
     */
    fun updateSmokingSensitivity(sensitivity: Float) {
        notificationPreferences.smokingDetectionSensitivity = sensitivity
    }
    
    /**
     * Updates high heart rate threshold
     */
    fun updateHighHeartRateThreshold(threshold: Int) {
        if (threshold in 80..200) {
            notificationPreferences.highHeartRateThreshold = threshold
        }
    }
    
    /**
     * Updates low heart rate threshold
     */
    fun updateLowHeartRateThreshold(threshold: Int) {
        if (threshold in 40..80) {
            notificationPreferences.lowHeartRateThreshold = threshold
        }
    }
    
    /**
     * Exports all data to CSV files
     */
    fun exportData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isExporting = true)
            
            try {
                val exportDir = File(context.getExternalFilesDir(null), "exports")
                if (!exportDir.exists()) {
                    exportDir.mkdirs()
                }
                
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                
                // Export smoking events
                val smokingEventsFile = File(exportDir, "smoking_events_$timestamp.csv")
                exportSmokingEvents(smokingEventsFile)
                
                // Export heart rate data
                val heartRateFile = File(exportDir, "heart_rate_data_$timestamp.csv")
                exportHeartRateData(heartRateFile)
                
                // Create a zip file or share individual files
                shareExportedFiles(listOf(smokingEventsFile, heartRateFile))
                
                _messages.emit("Data exported successfully")
            } catch (e: Exception) {
                _messages.emit("Error exporting data: ${e.message}")
            } finally {
                _uiState.value = _uiState.value.copy(isExporting = false)
            }
        }
    }
    
    /**
     * Clears all data from the database
     */
    fun clearAllData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isClearing = true)
            
            try {
                withContext(Dispatchers.IO) {
                    database.clearAllTables()
                }
                _messages.emit("All data cleared successfully")
            } catch (e: Exception) {
                _messages.emit("Error clearing data: ${e.message}")
            } finally {
                _uiState.value = _uiState.value.copy(isClearing = false)
            }
        }
    }
    
    private suspend fun exportSmokingEvents(file: File) {
        withContext(Dispatchers.IO) {
            val smokingEvents = smokingEventRepository.getAllSmokingEventsSync()
            
            FileWriter(file).use { writer ->
                // Write CSV header
                writer.append("ID,Start Time,End Time,Duration,Confidence,Detection Method,Location,Latitude,Longitude,Puff Count,Average Heart Rate,Peak Heart Rate,Baseline Heart Rate,Notes,Is Confirmed,Is False Positive\n")
                
                // Write data rows
                smokingEvents.forEach { event ->
                    writer.append("${event.id},")
                    writer.append("${event.startTime},")
                    writer.append("${event.endTime ?: ""},")
                    writer.append("${event.duration ?: ""},")
                    writer.append("${event.confidence},")
                    writer.append("${event.detectionMethod},")
                    writer.append("${event.location ?: ""},")
                    writer.append("${event.latitude ?: ""},")
                    writer.append("${event.longitude ?: ""},")
                    writer.append("${event.puffCount},")
                    writer.append("${event.averageHeartRate ?: ""},")
                    writer.append("${event.peakHeartRate ?: ""},")
                    writer.append("${event.baselineHeartRate ?: ""},")
                    writer.append("\"${event.notes ?: ""}\",")
                    writer.append("${event.isConfirmed},")
                    writer.append("${event.isFalsePositive}\n")
                }
            }
        }
    }
    
    private suspend fun exportHeartRateData(file: File) {
        withContext(Dispatchers.IO) {
            val heartRateData = heartRateRepository.getAllHeartRateDataSync()
            
            FileWriter(file).use { writer ->
                // Write CSV header
                writer.append("ID,Heart Rate,Timestamp,Device ID,Device Type,Confidence,Metadata,Smoking Event ID,Is During Smoking,Is Anomaly\n")
                
                // Write data rows
                heartRateData.forEach { data ->
                    writer.append("${data.id},")
                    writer.append("${data.heartRate},")
                    writer.append("${data.timestamp},")
                    writer.append("${data.deviceId},")
                    writer.append("${data.deviceType},")
                    writer.append("${data.confidence},")
                    writer.append("\"${data.metadata ?: ""}\",")
                    writer.append("${data.smokingEventId ?: ""},")
                    writer.append("${data.isDuringSmokingEvent},")
                    writer.append("${data.isAnomalyDetected}\n")
                }
            }
        }
    }
    
    private fun shareExportedFiles(files: List<File>) {
        val uris = files.map { file ->
            FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
        }
        
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND_MULTIPLE
            type = "text/csv"
            putParcelableArrayListExtra(Intent.EXTRA_STREAM, ArrayList(uris))
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        
        val chooserIntent = Intent.createChooser(shareIntent, "Share exported data")
        chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(chooserIntent)
    }
    
    override fun onCleared() {
        super.onCleared()
        bleManager.cleanup()
    }
    
    /**
     * UI state for settings screen
     */
    data class SettingsUiState(
        val isScanning: Boolean = false,
        val isExporting: Boolean = false,
        val isClearing: Boolean = false
    )
}
