package com.thryve.exhale.detection

import android.util.Log
import com.thryve.exhale.data.entity.SensorData
import com.thryve.exhale.data.entity.SensorType
import com.thryve.exhale.data.entity.SmokingEvent
import com.thryve.exhale.data.entity.DetectionMethod
import com.thryve.exhale.data.repository.SmokingEventRepository
import com.thryve.exhale.sensor.SensorDataProcessor
import com.thryve.exhale.sensor.GestureAnalysis
import com.thryve.exhale.sensor.RepetitiveMotionAnalysis
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.Date
import java.util.concurrent.ConcurrentLinkedQueue
/**
 * Main engine for detecting smoking events using sensor data and pattern recognition
 */
class SmokingDetectionEngine(
    private val sensorDataProcessor: SensorDataProcessor,
    private val smokingEventRepository: SmokingEventRepository
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // Detection events
    private val _smokingDetected = MutableSharedFlow<SmokingDetectionResult>()
    val smokingDetected: SharedFlow<SmokingDetectionResult> = _smokingDetected.asSharedFlow()
    
    // Data buffers for analysis
    private val accelerometerBuffer = ConcurrentLinkedQueue<SensorData>()
    private val gyroscopeBuffer = ConcurrentLinkedQueue<SensorData>()
    private val magnetometerBuffer = ConcurrentLinkedQueue<SensorData>()
    
    // Detection state
    private var isDetectionActive = false
    private var currentSmokingEvent: SmokingEvent? = null
    private var lastDetectionTime = 0L
    
    // Configuration
    private var detectionSensitivity = DetectionSensitivity.MEDIUM
    private var bufferSize = 100 // Number of sensor readings to keep in buffer
    private var detectionCooldown = 30000L // 30 seconds between detections
    
    companion object {
        private const val TAG = "SmokingDetectionEngine"
        private const val MIN_GESTURE_CONFIDENCE = 0.6f
        private const val MIN_REPETITIVE_CONFIDENCE = 0.5f
    }
    
    /**
     * Starts the smoking detection engine
     */
    fun startDetection(sensitivity: DetectionSensitivity = DetectionSensitivity.MEDIUM) {
        if (isDetectionActive) {
            Log.w(TAG, "Detection already active")
            return
        }
        
        this.detectionSensitivity = sensitivity
        isDetectionActive = true
        
        Log.i(TAG, "Smoking detection started with sensitivity: $sensitivity")
    }
    
    /**
     * Stops the smoking detection engine
     */
    fun stopDetection() {
        isDetectionActive = false
        currentSmokingEvent = null
        clearBuffers()
        
        Log.i(TAG, "Smoking detection stopped")
    }
    
    /**
     * Processes new sensor data for smoking detection
     */
    fun processSensorData(sensorData: SensorData) {
        if (!isDetectionActive) return
        
        // Add to appropriate buffer
        when (sensorData.sensorType) {
            SensorType.ACCELEROMETER -> {
                addToBuffer(accelerometerBuffer, sensorData)
                analyzeForSmokingPatterns()
            }
            SensorType.GYROSCOPE -> {
                addToBuffer(gyroscopeBuffer, sensorData)
            }
            SensorType.MAGNETOMETER -> {
                addToBuffer(magnetometerBuffer, sensorData)
            }
            else -> { /* Handle other sensor types if needed */ }
        }
    }
    
    /**
     * Adds sensor data to buffer with size limit
     */
    private fun addToBuffer(buffer: ConcurrentLinkedQueue<SensorData>, data: SensorData) {
        buffer.offer(data)
        while (buffer.size > bufferSize) {
            buffer.poll()
        }
    }
    
    /**
     * Main analysis function for detecting smoking patterns
     */
    private fun analyzeForSmokingPatterns() {
        if (accelerometerBuffer.size < 20) return // Need minimum data for analysis
        
        scope.launch {
            try {
                val accelerometerData = accelerometerBuffer.toList()
                val gyroscopeData = gyroscopeBuffer.toList()
                
                // Analyze hand-to-mouth gesture
                val gestureAnalysis = sensorDataProcessor.analyzeHandToMouthGesture(
                    accelerometerData, gyroscopeData
                )
                
                // Analyze repetitive motion (puffing)
                val repetitiveAnalysis = sensorDataProcessor.detectRepetitiveMotion(
                    accelerometerData
                )
                
                // Combine analyses for final detection
                val detectionResult = combineAnalyses(gestureAnalysis, repetitiveAnalysis)
                
                if (detectionResult.isSmokingDetected && shouldTriggerDetection()) {
                    handleSmokingDetection(detectionResult)
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in smoking pattern analysis", e)
            }
        }
    }
    
    /**
     * Combines different analyses to determine if smoking is detected
     */
    private fun combineAnalyses(
        gestureAnalysis: GestureAnalysis,
        repetitiveAnalysis: RepetitiveMotionAnalysis
    ): SmokingDetectionResult {
        
        val gestureScore = if (gestureAnalysis.isHandToMouthGesture) {
            gestureAnalysis.confidence
        } else 0.0f
        
        val repetitiveScore = if (repetitiveAnalysis.hasRepetitivePattern) {
            repetitiveAnalysis.confidence
        } else 0.0f
        
        // Weighted combination of scores
        val combinedScore = (gestureScore * 0.7f) + (repetitiveScore * 0.3f)
        
        // Adjust threshold based on sensitivity
        val threshold = when (detectionSensitivity) {
            DetectionSensitivity.LOW -> 0.8f
            DetectionSensitivity.MEDIUM -> 0.6f
            DetectionSensitivity.HIGH -> 0.4f
        }
        
        val isDetected = combinedScore >= threshold
        
        val detectionMethod = when {
            gestureScore > repetitiveScore -> DetectionMethod.ACCELEROMETER
            repetitiveScore > 0 -> DetectionMethod.COMBINED_SENSORS
            else -> DetectionMethod.MACHINE_LEARNING
        }
        
        return SmokingDetectionResult(
            isSmokingDetected = isDetected,
            confidence = combinedScore,
            detectionMethod = detectionMethod,
            gestureAnalysis = gestureAnalysis,
            repetitiveAnalysis = repetitiveAnalysis,
            timestamp = Date()
        )
    }
    
    /**
     * Checks if enough time has passed since last detection to trigger a new one
     */
    private fun shouldTriggerDetection(): Boolean {
        val currentTime = System.currentTimeMillis()
        return currentTime - lastDetectionTime > detectionCooldown
    }
    
    /**
     * Handles a positive smoking detection
     */
    private suspend fun handleSmokingDetection(result: SmokingDetectionResult) {
        lastDetectionTime = System.currentTimeMillis()
        
        // Create smoking event
        val smokingEvent = SmokingEvent(
            startTime = result.timestamp,
            confidence = result.confidence,
            detectionMethod = result.detectionMethod,
            puffCount = estimatePuffCount(result.repetitiveAnalysis),
            notes = "Auto-detected via ${result.detectionMethod}"
        )
        
        // Save to database
        val eventId = smokingEventRepository.insertSmokingEvent(smokingEvent)
        currentSmokingEvent = smokingEvent.copy(id = eventId)
        
        // Mark sensor data as part of smoking event
        markSensorDataAsSmokingEvent(eventId)
        
        // Emit detection event
        _smokingDetected.emit(result.copy(smokingEventId = eventId))
        
        Log.i(TAG, "Smoking event detected with confidence: ${result.confidence}")
    }
    
    /**
     * Estimates puff count from repetitive motion analysis
     */
    private fun estimatePuffCount(analysis: RepetitiveMotionAnalysis): Int {
        return if (analysis.hasRepetitivePattern && analysis.frequency > 0) {
            // Estimate based on frequency and typical smoking duration
            val estimatedDuration = 30f // seconds
            (analysis.frequency * estimatedDuration).toInt().coerceIn(1, 20)
        } else 1
    }
    
    /**
     * Marks recent sensor data as part of a smoking event
     */
    private suspend fun markSensorDataAsSmokingEvent(smokingEventId: Long) {
        // This would update sensor data in the database to mark it as part of the smoking event
        // Implementation would depend on your specific requirements
        Log.d(TAG, "Marking sensor data as part of smoking event: $smokingEventId")
    }
    
    /**
     * Manually triggers a smoking event (for user-initiated logging)
     */
    suspend fun manualSmokingEvent(notes: String? = null): Long {
        val smokingEvent = SmokingEvent(
            startTime = Date(),
            confidence = 1.0f,
            detectionMethod = DetectionMethod.MANUAL,
            notes = notes ?: "Manually logged by user",
            isConfirmed = true
        )
        
        return smokingEventRepository.insertSmokingEvent(smokingEvent)
    }
    
    /**
     * Ends the current smoking event
     */
    suspend fun endCurrentSmokingEvent() {
        currentSmokingEvent?.let { event ->
            val updatedEvent = event.copy(
                endTime = Date(),
                duration = Date().time - event.startTime.time
            )
            smokingEventRepository.updateSmokingEvent(updatedEvent)
            currentSmokingEvent = null
            
            Log.i(TAG, "Smoking event ended, duration: ${updatedEvent.duration}ms")
        }
    }
    
    /**
     * Clears all sensor data buffers
     */
    private fun clearBuffers() {
        accelerometerBuffer.clear()
        gyroscopeBuffer.clear()
        magnetometerBuffer.clear()
    }
    
    /**
     * Updates detection sensitivity
     */
    fun updateSensitivity(sensitivity: DetectionSensitivity) {
        this.detectionSensitivity = sensitivity
        Log.i(TAG, "Detection sensitivity updated to: $sensitivity")
    }
    
    /**
     * Gets current detection statistics
     */
    fun getDetectionStats(): DetectionStats {
        return DetectionStats(
            isActive = isDetectionActive,
            sensitivity = detectionSensitivity,
            bufferSizes = mapOf(
                "accelerometer" to accelerometerBuffer.size,
                "gyroscope" to gyroscopeBuffer.size,
                "magnetometer" to magnetometerBuffer.size
            ),
            lastDetectionTime = if (lastDetectionTime > 0) Date(lastDetectionTime) else null,
            currentEvent = currentSmokingEvent
        )
    }
    
    /**
     * Cleanup resources
     */
    fun cleanup() {
        stopDetection()
        scope.cancel()
    }
}

/**
 * Detection sensitivity levels
 */
enum class DetectionSensitivity {
    LOW,    // Fewer false positives, might miss some events
    MEDIUM, // Balanced approach
    HIGH    // More sensitive, might have more false positives
}

/**
 * Result of smoking detection analysis
 */
data class SmokingDetectionResult(
    val isSmokingDetected: Boolean,
    val confidence: Float,
    val detectionMethod: DetectionMethod,
    val gestureAnalysis: GestureAnalysis,
    val repetitiveAnalysis: RepetitiveMotionAnalysis,
    val timestamp: Date,
    val smokingEventId: Long? = null
)

/**
 * Statistics about the detection engine
 */
data class DetectionStats(
    val isActive: Boolean,
    val sensitivity: DetectionSensitivity,
    val bufferSizes: Map<String, Int>,
    val lastDetectionTime: Date?,
    val currentEvent: SmokingEvent?
)
