package com.thryve.exhale.ui

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.thryve.exhale.R
import com.thryve.exhale.adapter.ConnectedDeviceAdapter
import com.thryve.exhale.bluetooth.BleManager
import com.thryve.exhale.data.entity.ConnectedDevice
import com.thryve.exhale.data.entity.HeartRateData
import com.thryve.exhale.data.entity.HeartRateZone
import com.thryve.exhale.data.repository.HeartRateRepository
import com.thryve.exhale.data.repository.SmokingEventRepository
import com.thryve.exhale.data.repository.SensorDataRepository
import com.thryve.exhale.data.database.ExhaleDatabase
import com.thryve.exhale.databinding.ActivityMainBinding
import com.thryve.exhale.detection.DetectionSensitivity
import com.thryve.exhale.notification.NotificationScheduler
import com.thryve.exhale.service.HeartRateMonitoringService
import com.thryve.exhale.service.SmokingDetectionService
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
class MainActivity : AppCompatActivity() {

    // TODO: Add dependency injection later
    private lateinit var bleManager: BleManager
    private lateinit var heartRateRepository: HeartRateRepository
    private lateinit var smokingEventRepository: SmokingEventRepository
    private lateinit var notificationScheduler: NotificationScheduler
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var deviceAdapter: ConnectedDeviceAdapter
    
    // Service connections
    private var heartRateService: HeartRateMonitoringService? = null
    private var smokingDetectionService: SmokingDetectionService? = null
    private var isHeartRateServiceBound = false
    private var isSmokingServiceBound = false
    
    companion object {
        private const val TAG = "MainActivity"
        private val REQUIRED_PERMISSIONS = arrayOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.BODY_SENSORS,
            Manifest.permission.ACTIVITY_RECOGNITION
        )
    }
    
    // Permission launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            initializeServices()
        } else {
            Toast.makeText(this, "Permissions required for app functionality", Toast.LENGTH_LONG).show()
        }
    }
    
    // Service connections
    private val heartRateServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as HeartRateMonitoringService.HeartRateServiceBinder
            heartRateService = binder.getService()
            isHeartRateServiceBound = true
            updateHeartRateUI()
            Log.d(TAG, "Heart rate service connected")
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            heartRateService = null
            isHeartRateServiceBound = false
            Log.d(TAG, "Heart rate service disconnected")
        }
    }
    
    private val smokingServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as SmokingDetectionService.SmokingDetectionServiceBinder
            smokingDetectionService = binder.getService()
            isSmokingServiceBound = true
            updateSmokingDetectionUI()
            Log.d(TAG, "Smoking detection service connected")
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            smokingDetectionService = null
            isSmokingServiceBound = false
            Log.d(TAG, "Smoking detection service disconnected")
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        setupNotifications()
        checkPermissionsAndInitialize()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = "Exhale - Health Monitor"
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                startActivity(Intent(this, SettingsActivity::class.java))
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun setupNotifications() {
        notificationScheduler = NotificationScheduler(this)
        notificationScheduler.scheduleAllNotifications()
        Log.d(TAG, "Notification scheduler initialized")
    }
    
    private fun setupRecyclerView() {
        deviceAdapter = ConnectedDeviceAdapter { device ->
            showDeviceDetails(device)
        }
        
        binding.rvConnectedDevices.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = deviceAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.switchSmokingDetection.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                startSmokingDetection()
            } else {
                stopSmokingDetection()
            }
        }
        
        binding.btnManualLog.setOnClickListener {
            logManualSmokingEvent()
        }
        
        binding.btnScanDevices.setOnClickListener {
            scanForDevices()
        }
        
        binding.btnViewHistory.setOnClickListener {
            Toast.makeText(this, "History screen coming soon", Toast.LENGTH_SHORT).show()
        }
        
        binding.btnSettings.setOnClickListener {
            Toast.makeText(this, "Settings screen coming soon", Toast.LENGTH_SHORT).show()
        }
        
        binding.fabEmergencyStop.setOnClickListener {
            emergencyStop()
        }
    }
    
    private fun checkPermissionsAndInitialize() {
        val missingPermissions = REQUIRED_PERMISSIONS.filter {
            ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isEmpty()) {
            initializeServices()
        } else {
            permissionLauncher.launch(missingPermissions.toTypedArray())
        }
    }
    
    private fun initializeServices() {
        // Initialize database and repositories
        val database = ExhaleDatabase.getDatabase(this)
        heartRateRepository = HeartRateRepository(database.heartRateDao())
        smokingEventRepository = SmokingEventRepository(database.smokingEventDao())
        val sensorDataRepository = SensorDataRepository(database.sensorDataDao(), database.connectedDeviceDao())

        // Initialize BLE manager
        bleManager = BleManager(this, sensorDataRepository, heartRateRepository)

        bindHeartRateService()
        bindSmokingDetectionService()
        observeHeartRateData()
        observeSmokingEvents()
        observeDeviceConnections()
        updateUI()
    }
    
    private fun bindHeartRateService() {
        val intent = Intent(this, HeartRateMonitoringService::class.java)
        bindService(intent, heartRateServiceConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun bindSmokingDetectionService() {
        val intent = Intent(this, SmokingDetectionService::class.java)
        bindService(intent, smokingServiceConnection, Context.BIND_AUTO_CREATE)
    }
    
    private fun observeHeartRateData() {
        // TODO: Implement when dependency injection is set up
        binding.tvCurrentHeartRate.text = "--"
        binding.tvHeartRateStatus.text = "Not connected"
    }

    private fun observeSmokingEvents() {
        // TODO: Implement when dependency injection is set up
        binding.tvTodaySmokingCount.text = "0"
        binding.tvWeekSmokingCount.text = "0"
        binding.tvLastSmokingEvent.text = "--"
    }

    private fun observeDeviceConnections() {
        // TODO: Implement when dependency injection is set up
        binding.tvHeartRateStatus.text = "Not connected"
        // deviceAdapter.updateDevices(emptyList()) // TODO: Fix when adapter is properly initialized
    }
    
    private fun updateHeartRateDisplay(heartRateData: HeartRateData) {
        binding.tvCurrentHeartRate.text = heartRateData.heartRate.toString()
        
        // Update heart rate zone
        val zone = heartRateData.getHeartRateZone()
        binding.tvHeartRateZone.text = "Heart Rate Zone: ${zone.name}"
        
        // Update heart rate color based on zone
        val color = when (zone) {
            HeartRateZone.NORMAL -> ContextCompat.getColor(this, R.color.heart_rate_normal)
            HeartRateZone.ELEVATED -> ContextCompat.getColor(this, R.color.heart_rate_elevated)
            HeartRateZone.HIGH -> ContextCompat.getColor(this, R.color.heart_rate_high)
            else -> ContextCompat.getColor(this, R.color.heart_rate_color)
        }
        binding.tvCurrentHeartRate.setTextColor(color)
        
        // Update status
        binding.tvHeartRateStatus.text = "Connected"
        binding.tvHeartRateStatus.setTextColor(ContextCompat.getColor(this, R.color.status_connected))
    }
    
    private fun updateHeartRateUI() {
        heartRateService?.let { service ->
            val status = service.getMonitoringStatus()
            
            if (status.baselineHeartRate > 0) {
                binding.tvBaselineHeartRate.text = "${status.baselineHeartRate} BPM"
            }
            
            if (status.isCalculatingBaseline) {
                binding.tvHeartRateStatus.text = "Calculating baseline..."
                binding.tvHeartRateStatus.setTextColor(ContextCompat.getColor(this, R.color.status_scanning))
            }
        }
    }
    
    private fun updateSmokingDetectionUI() {
        smokingDetectionService?.let { service ->
            val status = service.getDetectionStatus()
            binding.switchSmokingDetection.isChecked = status.isDetecting
        }
    }
    
    private fun updateDeviceConnectionUI() {
        val hasDevices = deviceAdapter.itemCount > 0
        binding.tvNoDevices.visibility = if (hasDevices) android.view.View.GONE else android.view.View.VISIBLE
        binding.rvConnectedDevices.visibility = if (hasDevices) android.view.View.VISIBLE else android.view.View.GONE
    }
    
    private fun updateUI() {
        // Initial UI updates
        updateHeartRateUI()
        updateSmokingDetectionUI()
        updateDeviceConnectionUI()
    }
    
    private fun startSmokingDetection() {
        val intent = Intent(this, SmokingDetectionService::class.java).apply {
            action = SmokingDetectionService.ACTION_START_DETECTION
        }
        startForegroundService(intent)
        Toast.makeText(this, "Smoking detection started", Toast.LENGTH_SHORT).show()
    }
    
    private fun stopSmokingDetection() {
        val intent = Intent(this, SmokingDetectionService::class.java).apply {
            action = SmokingDetectionService.ACTION_STOP_DETECTION
        }
        startService(intent)
        Toast.makeText(this, "Smoking detection stopped", Toast.LENGTH_SHORT).show()
    }
    
    private fun logManualSmokingEvent() {
        val intent = Intent(this, SmokingDetectionService::class.java).apply {
            action = SmokingDetectionService.ACTION_MANUAL_LOG
        }
        startService(intent)
        Toast.makeText(this, "Smoking event logged", Toast.LENGTH_SHORT).show()
    }
    
    private fun scanForDevices() {
        if (bleManager.isBluetoothAvailable()) {
            bleManager.startScanning()
            binding.btnScanDevices.text = "Scanning..."
            binding.btnScanDevices.isEnabled = false
            
            // Re-enable button after scan timeout
            binding.btnScanDevices.postDelayed({
                binding.btnScanDevices.text = "Scan"
                binding.btnScanDevices.isEnabled = true
            }, 30000) // 30 seconds
            
            Toast.makeText(this, "Scanning for devices...", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "Bluetooth not available", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showDeviceDetails(device: ConnectedDevice) {
        // TODO: Show device details dialog or navigate to device details screen
        Toast.makeText(this, "Device: ${device.deviceName}", Toast.LENGTH_SHORT).show()
    }
    
    private fun emergencyStop() {
        // Stop all services
        stopSmokingDetection()
        
        val heartRateIntent = Intent(this, HeartRateMonitoringService::class.java).apply {
            action = HeartRateMonitoringService.ACTION_STOP_MONITORING
        }
        startService(heartRateIntent)
        
        // Disconnect all devices
        bleManager.disconnectAllDevices()
        
        Toast.makeText(this, "All monitoring stopped", Toast.LENGTH_SHORT).show()
        Log.i(TAG, "Emergency stop activated")
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        if (isHeartRateServiceBound) {
            unbindService(heartRateServiceConnection)
        }
        
        if (isSmokingServiceBound) {
            unbindService(smokingServiceConnection)
        }
    }
}
