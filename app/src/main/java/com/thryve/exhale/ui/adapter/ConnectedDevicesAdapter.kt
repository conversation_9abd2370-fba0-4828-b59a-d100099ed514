package com.thryve.exhale.ui.adapter

import android.bluetooth.BluetoothDevice
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.thryve.exhale.R
import com.thryve.exhale.databinding.ItemConnectedDeviceBinding
import com.thryve.exhale.viewmodel.DeviceManagementViewModel

/**
 * Adapter for displaying connected Bluetooth devices
 */
class ConnectedDevicesAdapter(
    private val onDisconnectClick: (BluetoothDevice) -> Unit,
    private val onSettingsClick: (BluetoothDevice) -> Unit
) : ListAdapter<DeviceManagementViewModel.ConnectedDeviceInfo, ConnectedDevicesAdapter.ViewHolder>(DiffCallback) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemConnectedDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class ViewHolder(
        private val binding: ItemConnectedDeviceBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(deviceInfo: DeviceManagementViewModel.ConnectedDeviceInfo) {
            val device = deviceInfo.device
            
            binding.tvDeviceName.text = device.name ?: "Unknown Device"
            binding.tvDeviceAddress.text = device.address
            
            val statusText = buildString {
                append(deviceInfo.connectionStatus)
                deviceInfo.lastHeartRate?.let { heartRate ->
                    append(" • Heart Rate: $heartRate BPM")
                }
            }
            binding.tvConnectionStatus.text = statusText
            
            // Set device icon based on device type
            val iconRes = when {
                device.name?.lowercase()?.contains("watch") == true -> R.drawable.ic_watch
                device.name?.lowercase()?.contains("band") == true -> R.drawable.ic_fitness_band
                device.name?.lowercase()?.contains("heart") == true -> R.drawable.ic_heart_rate
                else -> R.drawable.ic_bluetooth
            }
            binding.ivDeviceIcon.setImageResource(iconRes)
            
            binding.btnDisconnect.setOnClickListener {
                onDisconnectClick(device)
            }
            
            binding.btnSettings.setOnClickListener {
                onSettingsClick(device)
            }
        }
    }
    
    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<DeviceManagementViewModel.ConnectedDeviceInfo>() {
            override fun areItemsTheSame(
                oldItem: DeviceManagementViewModel.ConnectedDeviceInfo,
                newItem: DeviceManagementViewModel.ConnectedDeviceInfo
            ): Boolean {
                return oldItem.device.address == newItem.device.address
            }
            
            override fun areContentsTheSame(
                oldItem: DeviceManagementViewModel.ConnectedDeviceInfo,
                newItem: DeviceManagementViewModel.ConnectedDeviceInfo
            ): Boolean {
                return oldItem == newItem
            }
        }
    }
}
