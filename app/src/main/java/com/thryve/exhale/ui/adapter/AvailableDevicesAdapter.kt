package com.thryve.exhale.ui.adapter

import android.bluetooth.BluetoothDevice
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.thryve.exhale.R
import com.thryve.exhale.databinding.ItemAvailableDeviceBinding
import com.thryve.exhale.viewmodel.DeviceManagementViewModel

/**
 * Adapter for displaying available Bluetooth devices
 */
class AvailableDevicesAdapter(
    private val onConnectClick: (BluetoothDevice) -> Unit
) : ListAdapter<DeviceManagementViewModel.AvailableDeviceInfo, AvailableDevicesAdapter.ViewHolder>(DiffCallback) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemAvailableDeviceBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class ViewHolder(
        private val binding: ItemAvailableDeviceBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(deviceInfo: DeviceManagementViewModel.AvailableDeviceInfo) {
            val device = deviceInfo.device
            
            binding.tvDeviceName.text = device.name ?: "Unknown Device"
            binding.tvDeviceAddress.text = device.address
            binding.tvSignalStrength.text = deviceInfo.signalStrength
            binding.tvDeviceType.text = deviceInfo.deviceType
            
            // Set device icon based on device type
            val iconRes = when {
                device.name?.lowercase()?.contains("watch") == true -> R.drawable.ic_watch
                device.name?.lowercase()?.contains("band") == true -> R.drawable.ic_fitness_band
                device.name?.lowercase()?.contains("heart") == true -> R.drawable.ic_heart_rate
                else -> R.drawable.ic_bluetooth
            }
            binding.ivDeviceIcon.setImageResource(iconRes)
            
            binding.btnConnect.setOnClickListener {
                onConnectClick(device)
            }
        }
    }
    
    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<DeviceManagementViewModel.AvailableDeviceInfo>() {
            override fun areItemsTheSame(
                oldItem: DeviceManagementViewModel.AvailableDeviceInfo,
                newItem: DeviceManagementViewModel.AvailableDeviceInfo
            ): Boolean {
                return oldItem.device.address == newItem.device.address
            }
            
            override fun areContentsTheSame(
                oldItem: DeviceManagementViewModel.AvailableDeviceInfo,
                newItem: DeviceManagementViewModel.AvailableDeviceInfo
            ): Boolean {
                return oldItem == newItem
            }
        }
    }
}
