package com.thryve.exhale.ui

import android.Manifest
import android.bluetooth.BluetoothDevice
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.thryve.exhale.R
import com.thryve.exhale.databinding.ActivityDeviceManagementBinding
import com.thryve.exhale.ui.adapter.AvailableDevicesAdapter
import com.thryve.exhale.ui.adapter.ConnectedDevicesAdapter
import com.thryve.exhale.viewmodel.DeviceManagementViewModel
import kotlinx.coroutines.launch

/**
 * Activity for managing Bluetooth device connections
 */
class DeviceManagementActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityDeviceManagementBinding
    private val viewModel: DeviceManagementViewModel by viewModels()
    
    private lateinit var connectedDevicesAdapter: ConnectedDevicesAdapter
    private lateinit var availableDevicesAdapter: AvailableDevicesAdapter
    
    private val bluetoothPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            startDeviceScan()
        } else {
            Toast.makeText(this, "Bluetooth permissions required for device scanning", Toast.LENGTH_LONG).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDeviceManagementBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupToolbar()
        setupRecyclerViews()
        setupClickListeners()
        observeViewModel()
        
        viewModel.loadConnectedDevices()
    }
    
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupRecyclerViews() {
        // Connected devices
        connectedDevicesAdapter = ConnectedDevicesAdapter(
            onDisconnectClick = { device -> viewModel.disconnectDevice(device) },
            onSettingsClick = { device -> showDeviceSettings(device) }
        )
        
        binding.rvConnectedDevices.apply {
            layoutManager = LinearLayoutManager(this@DeviceManagementActivity)
            adapter = connectedDevicesAdapter
        }
        
        // Available devices
        availableDevicesAdapter = AvailableDevicesAdapter(
            onConnectClick = { device -> viewModel.connectToDevice(device) }
        )
        
        binding.rvAvailableDevices.apply {
            layoutManager = LinearLayoutManager(this@DeviceManagementActivity)
            adapter = availableDevicesAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.btnScanDevices.setOnClickListener {
            if (hasBluetoothPermissions()) {
                startDeviceScan()
            } else {
                requestBluetoothPermissions()
            }
        }
        
        binding.fabAddDevice.setOnClickListener {
            // TODO: Show manual device addition dialog
            Toast.makeText(this, "Manual device addition coming soon", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
        
        lifecycleScope.launch {
            viewModel.connectedDevices.collect { devices ->
                connectedDevicesAdapter.submitList(devices)
                binding.tvNoConnectedDevices.visibility = if (devices.isEmpty()) View.VISIBLE else View.GONE
            }
        }
        
        lifecycleScope.launch {
            viewModel.availableDevices.collect { devices ->
                availableDevicesAdapter.submitList(devices)
                binding.tvNoAvailableDevices.visibility = if (devices.isEmpty() && !viewModel.uiState.value.isScanning) View.VISIBLE else View.GONE
            }
        }
        
        lifecycleScope.launch {
            viewModel.messages.collect { message ->
                Toast.makeText(this@DeviceManagementActivity, message, Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun updateUI(state: DeviceManagementViewModel.DeviceManagementUiState) {
        // Update scanning UI
        binding.btnScanDevices.isEnabled = !state.isScanning
        binding.btnScanDevices.text = if (state.isScanning) "Scanning..." else "Scan"
        
        binding.progressScanning.visibility = if (state.isScanning) View.VISIBLE else View.GONE
        binding.tvScanningStatus.visibility = if (state.isScanning) View.VISIBLE else View.GONE
        
        // Update available devices visibility
        if (state.isScanning) {
            binding.tvNoAvailableDevices.visibility = View.GONE
        }
    }
    
    private fun startDeviceScan() {
        viewModel.startDeviceScan()
    }
    
    private fun hasBluetoothPermissions(): Boolean {
        val permissions = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
        
        return permissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
    }
    
    private fun requestBluetoothPermissions() {
        val permissions = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
        
        bluetoothPermissionLauncher.launch(permissions)
    }
    
    private fun showDeviceSettings(device: BluetoothDevice) {
        // TODO: Show device-specific settings dialog
        Toast.makeText(this, "Device settings for ${device.name}", Toast.LENGTH_SHORT).show()
    }
}
