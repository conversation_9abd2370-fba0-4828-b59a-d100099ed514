package com.thryve.exhale.ui

import android.app.TimePickerDialog
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.thryve.exhale.R
import com.thryve.exhale.databinding.ActivitySettingsBinding
import com.thryve.exhale.notification.NotificationPreferences
import com.thryve.exhale.viewmodel.SettingsViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * Settings and configuration activity
 */
class SettingsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivitySettingsBinding
    private val viewModel: SettingsViewModel by viewModels()
    private lateinit var notificationPreferences: NotificationPreferences
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        notificationPreferences = NotificationPreferences(this)
        
        setupToolbar()
        setupViews()
        observeViewModel()
        loadCurrentSettings()
    }
    
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupViews() {
        // Device management buttons
        binding.btnManageDevices.setOnClickListener {
            startActivity(Intent(this, DeviceManagementActivity::class.java))
        }
        
        binding.btnScanDevices.setOnClickListener {
            viewModel.startDeviceScan()
        }
        
        // Detection sensitivity slider
        binding.sliderSmokingSensitivity.addOnChangeListener { _, value, _ ->
            viewModel.updateSmokingSensitivity(value)
        }
        
        // Heart rate threshold inputs
        binding.etHighHeartRateThreshold.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val threshold = binding.etHighHeartRateThreshold.text.toString().toIntOrNull()
                threshold?.let { viewModel.updateHighHeartRateThreshold(it) }
            }
        }
        
        binding.etLowHeartRateThreshold.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val threshold = binding.etLowHeartRateThreshold.text.toString().toIntOrNull()
                threshold?.let { viewModel.updateLowHeartRateThreshold(it) }
            }
        }
        
        // Notification switches
        binding.switchSmokingAlerts.setOnCheckedChangeListener { _, isChecked ->
            notificationPreferences.smokingAlertsEnabled = isChecked
        }
        
        binding.switchHeartRateAlerts.setOnCheckedChangeListener { _, isChecked ->
            notificationPreferences.heartRateAlertsEnabled = isChecked
        }
        
        binding.switchHealthRecommendations.setOnCheckedChangeListener { _, isChecked ->
            notificationPreferences.healthRecommendationsEnabled = isChecked
        }
        
        binding.switchDailySummary.setOnCheckedChangeListener { _, isChecked ->
            notificationPreferences.dailySummaryEnabled = isChecked
        }
        
        binding.switchMotivationalMessages.setOnCheckedChangeListener { _, isChecked ->
            notificationPreferences.motivationalMessagesEnabled = isChecked
        }
        
        // Daily summary time picker
        binding.btnDailySummaryTime.setOnClickListener {
            showTimePicker()
        }
        
        // Data management buttons
        binding.btnExportData.setOnClickListener {
            exportData()
        }
        
        binding.btnClearData.setOnClickListener {
            showClearDataConfirmation()
        }
    }
    
    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUI(state)
            }
        }
        
        lifecycleScope.launch {
            viewModel.messages.collect { message ->
                Toast.makeText(this@SettingsActivity, message, Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun loadCurrentSettings() {
        // Load notification preferences
        binding.switchSmokingAlerts.isChecked = notificationPreferences.smokingAlertsEnabled
        binding.switchHeartRateAlerts.isChecked = notificationPreferences.heartRateAlertsEnabled
        binding.switchHealthRecommendations.isChecked = notificationPreferences.healthRecommendationsEnabled
        binding.switchDailySummary.isChecked = notificationPreferences.dailySummaryEnabled
        binding.switchMotivationalMessages.isChecked = notificationPreferences.motivationalMessagesEnabled
        
        // Load detection settings
        binding.sliderSmokingSensitivity.value = notificationPreferences.smokingDetectionSensitivity
        binding.etHighHeartRateThreshold.setText(notificationPreferences.highHeartRateThreshold.toString())
        binding.etLowHeartRateThreshold.setText(notificationPreferences.lowHeartRateThreshold.toString())
        
        // Load daily summary time
        updateDailySummaryTimeButton()
    }
    
    private fun updateUI(state: SettingsViewModel.SettingsUiState) {
        binding.btnScanDevices.isEnabled = !state.isScanning
        binding.btnScanDevices.text = if (state.isScanning) "Scanning..." else "Scan for New Devices"
        
        binding.btnExportData.isEnabled = !state.isExporting
        binding.btnExportData.text = if (state.isExporting) "Exporting..." else "Export Data"
        
        binding.btnClearData.isEnabled = !state.isClearing
    }
    
    private fun showTimePicker() {
        val currentHour = notificationPreferences.dailySummaryTime
        
        val timePickerDialog = TimePickerDialog(
            this,
            { _, hourOfDay, minute ->
                val newCalendar = Calendar.getInstance()
                newCalendar.set(Calendar.HOUR_OF_DAY, hourOfDay)
                newCalendar.set(Calendar.MINUTE, minute)
                
                notificationPreferences.dailySummaryTime = hourOfDay
                updateDailySummaryTimeButton()
            },
            currentHour,
            0,
            false
        )
        
        timePickerDialog.show()
    }
    
    private fun updateDailySummaryTimeButton() {
        binding.btnDailySummaryTime.text = notificationPreferences.getDailySummaryTimeString()
    }
    
    private fun exportData() {
        viewModel.exportData()
    }
    
    private fun showClearDataConfirmation() {
        MaterialAlertDialogBuilder(this)
            .setTitle("Clear All Data")
            .setMessage("This will permanently delete all smoking events, heart rate data, and sensor data. This action cannot be undone.")
            .setPositiveButton("Clear Data") { _, _ ->
                viewModel.clearAllData()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
}
